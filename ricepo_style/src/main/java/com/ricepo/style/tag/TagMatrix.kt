package com.ricepo.style.tag

class TagMatrix {
  private val matrix: Array<FloatArray>
  private var column = 0
  private var row = 0

  companion object {
    private fun make(column: Int, row: Int): TagMatrix {
      val matrix = TagMatrix()
      matrix.column = column
      matrix.row = row
      for (i in 0 until column) {
        for (j in 0 until row) {
          matrix.matrix[i][j] = 0f
        }
      }
      return matrix
    }

    private fun makeFromArray(column: Int, row: Int, arr: Array<FloatArray>): TagMatrix {
      val matrix = make(column, row)
      for (i in 0 until column) {
        for (j in 0 until row) {
          val position = i * row + j
          matrix.matrix[i][j] = arr[position / row][position % row]
        }
      }
      return matrix
    }

    private fun multiply(m1: TagMatrix, m2: TagMatrix): TagMatrix {
      val result = make(m1.column, m2.row)
      for (i in 0 until m1.column) {
        for (j in 0 until m2.row) {
          for (k in 0 until m1.row) {
            result.matrix[i][j] += m1.matrix[i][k] * m2.matrix[k][j]
          }
        }
      }
      return result
    }

    fun pointRotation(point: TagPoint, direction: TagPoint, angle: Float): TagPoint {
      if (angle == 0f) {
        return point
      }
      val temp2 =
        arrayOf(floatArrayOf(point.x, point.y, point.z, 1f))
      var result = makeFromArray(1, 4, temp2)
      if (direction.z * direction.z + direction.y * direction.y != 0f) {
        val cos1 =
          (direction.z / Math.sqrt(direction.z * direction.z + direction.y * direction.y.toDouble())).toFloat()
        val sin1 =
          (direction.y / Math.sqrt(direction.z * direction.z + direction.y * direction.y.toDouble())).toFloat()
        val t1 = arrayOf(
          floatArrayOf(1f, 0f, 0f, 0f),
          floatArrayOf(0f, cos1, sin1, 0f),
          floatArrayOf(0f, -sin1, cos1, 0f),
          floatArrayOf(0f, 0f, 0f, 1f)
        )
        val m1 = makeFromArray(4, 4, t1)
        result = multiply(result, m1)
      }
      if (direction.x * direction.x + direction.y * direction.y + direction.z * direction.z != 0f) {
        val cos2 =
          (
            Math.sqrt(direction.y * direction.y + direction.z * direction.z.toDouble()) / Math.sqrt(
              direction.x * direction.x + direction.y * direction.y + (direction.z * direction.z).toDouble()
            )
            ).toFloat()
        val sin2 =
          (-direction.x / Math.sqrt(direction.x * direction.x + direction.y * direction.y + (direction.z * direction.z).toDouble())).toFloat()
        val t2 = arrayOf(
          floatArrayOf(cos2, 0f, -sin2, 0f),
          floatArrayOf(0f, 1f, 0f, 0f),
          floatArrayOf(sin2, 0f, cos2, 0f),
          floatArrayOf(0f, 0f, 0f, 1f)
        )
        val m2 = makeFromArray(4, 4, t2)
        result = multiply(result, m2)
      }
      val cos3 = Math.cos(angle.toDouble()).toFloat()
      val sin3 = Math.sin(angle.toDouble()).toFloat()
      val t3 = arrayOf(
        floatArrayOf(cos3, sin3, 0f, 0f),
        floatArrayOf(-sin3, cos3, 0f, 0f),
        floatArrayOf(0f, 0f, 1f, 0f),
        floatArrayOf(0f, 0f, 0f, 1f)
      )
      val m3 = makeFromArray(4, 4, t3)
      result = multiply(result, m3)
      if (direction.x * direction.x + direction.y * direction.y + direction.z * direction.z != 0f) {
        val cos2 =
          (
            Math.sqrt(direction.y * direction.y + direction.z * direction.z.toDouble()) / Math.sqrt(
              direction.x * direction.x + direction.y * direction.y + (direction.z * direction.z).toDouble()
            )
            ).toFloat()
        val sin2 =
          (-direction.x / Math.sqrt(direction.x * direction.x + direction.y * direction.y + (direction.z * direction.z).toDouble())).toFloat()
        val t2_ = arrayOf(
          floatArrayOf(cos2, 0f, sin2, 0f),
          floatArrayOf(0f, 1f, 0f, 0f),
          floatArrayOf(-sin2, 0f, cos2, 0f),
          floatArrayOf(0f, 0f, 0f, 1f)
        )
        val m2_ = makeFromArray(4, 4, t2_)
        result = multiply(result, m2_)
      }
      if (direction.z * direction.z + direction.y * direction.y != 0f) {
        val cos1 =
          (direction.z / Math.sqrt(direction.z * direction.z + direction.y * direction.y.toDouble())).toFloat()
        val sin1 =
          (direction.y / Math.sqrt(direction.z * direction.z + direction.y * direction.y.toDouble())).toFloat()
        val t1_ = arrayOf(
          floatArrayOf(1f, 0f, 0f, 0f),
          floatArrayOf(0f, cos1, -sin1, 0f),
          floatArrayOf(0f, sin1, cos1, 0f),
          floatArrayOf(0f, 0f, 0f, 1f)
        )
        val m1_ = makeFromArray(4, 4, t1_)
        result = multiply(result, m1_)
      }
      return TagPoint.Companion.make(
        result.matrix[0][0],
        result.matrix[0][1],
        result.matrix[0][2]
      )
    }
  }

  init {
    matrix = Array(4) { FloatArray(4) }
  }
}
