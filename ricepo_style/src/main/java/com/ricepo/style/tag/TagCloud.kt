package com.ricepo.style.tag

import android.os.Build
import java.util.Collections

class TagCloud @JvmOverloads constructor(
  tags: MutableList<Tag>?,
  radius: Int = DEFAULT_RADIUS,
  tagColorLight: FloatArray = DEFAULT_COLOR_DARK,
  tagColorDark: FloatArray = DEFAULT_COLOR_LIGHT
) {
  private class TagComparator : Comparator<Tag> {
    override fun compare(tag: Tag, tag2: Tag): Int {
      return if (tag.scale > tag2.scale) 1 else -1
    }
  }

  private var cos_mAngleX = 0f
  private var cos_mAngleY = 0f
  private var cos_mAngleZ = 0f
  private var distrEven = false
  private var largest = 0
  private var mAngleX = 0f
  private var mAngleY = 0f
  private var mAngleZ = 0f
  private var radius = 0
  private var sin_mAngleX = 0f
  private var sin_mAngleY = 0f
  private var sin_mAngleZ = 0f
  private var smallest = 0
  private var tagCloud: MutableList<Tag>? = null
  private var tagColorDark: FloatArray
  private var tagColorLight: FloatArray
  private val youngPoints: ArrayList<*>? = null

  @JvmOverloads
  constructor(radius: Int = DEFAULT_RADIUS) : this(
    ArrayList<Tag>(),
    radius
  ) {
  }

  fun add(tag: Tag) {
    initTag(tag)
    tagCloud!!.add(tag)
  }

  fun clear() {
    tagCloud!!.clear()
  }

  fun create(distrEven: Boolean) {
    this.distrEven = distrEven
    positionAll(distrEven)
    smallest = 9999
    largest = 0
    for (i in tagCloud!!.indices) {
      val popularity = tagCloud!![i].popularity
      largest = Math.max(largest, popularity)
      smallest = Math.min(smallest, popularity)
      initTag(tagCloud!![i])
    }
  }

  operator fun get(position: Int): Tag {
    return tagCloud!![position]
  }

  private fun getColorFromGradient(progress: Float): FloatArray {
    val arr = FloatArray(4)
    arr[0] = 1f
    val v1 = 1f - progress
    arr[1] = tagColorDark[0] * progress + tagColorLight[0] * v1
    arr[2] = tagColorDark[1] * progress + tagColorLight[1] * v1
    arr[3] = progress * tagColorDark[2] + v1 * tagColorLight[2]
    return arr
  }

  private fun getPercentage(tag: Tag): Float {
    val popularity = tag.popularity
    return if (smallest == largest) 1f else (popularity - smallest) * 10f / (largest - smallest)
  }

  val tagList: List<Tag>?
    get() = tagCloud

  val top: Tag
    get() = this[tagCloud!!.size - 1]

  fun indexOf(tag: Tag): Int {
    return tagCloud!!.indexOf(tag)
  }

  private fun initTag(tag: Tag) {
    tag.setColorByArray(getColorFromGradient(getPercentage(tag)))
  }

  private fun position(drilEven: Boolean, tag: Tag) {
    tagCloud!!.size
    val r1 = Math.random() * Math.PI
    val r2 = Math.random() * Math.PI * 2
    tag.locX = (radius * Math.cos(r2) * Math.sin(r1)).toFloat()
    tag.locY = (radius * Math.sin(r2) * Math.sin(r1)).toFloat()
    tag.locZ = (radius * Math.cos(r1)).toFloat()
  }

  private fun positionAll(drilEven: Boolean) {
    var pos: Double
    val size = tagCloud!!.size
    for (i in 1 until size + 1) {
      var doublePI: Double
      if (drilEven) {
        pos = Math.acos((i * 2.0f - 1) / size - 1.toDouble())
        doublePI = Math.sqrt(size * Math.PI) * pos
      } else {
        pos = Math.random() * Math.PI
        doublePI = Math.random() * Math.PI * 2
      }
      val position = i - 1
      tagCloud!![position].locX = (radius * Math.cos(doublePI) * Math.sin(pos)).toFloat()
      tagCloud!![position].locY = (radius * Math.sin(doublePI) * Math.sin(pos)).toFloat()
      tagCloud!![position].locZ = (radius * Math.cos(pos)).toFloat()
    }
  }

  fun reset() {
    create(distrEven)
  }

  fun setAngleX(angleX: Float) {
    mAngleX = angleX
  }

  fun setAngleY(angleY: Float) {
    mAngleY = angleY
  }

  fun setRadius(radius: Int) {
    this.radius = radius
  }

  fun setTagColorDark(tagColorDark: FloatArray) {
    this.tagColorDark = tagColorDark
  }

  fun setTagColorLight(tagColorLight: FloatArray) {
    this.tagColorLight = tagColorLight
  }

  fun setTagList(tag: MutableList<Tag>?) {
    tagCloud = tag
  }

  private fun sineCosine(x: Float, y: Float, z: Float) {
    val v0 = x.toDouble() * Math.PI / 180
    sin_mAngleX = Math.sin(v0).toFloat()
    cos_mAngleX = Math.cos(v0).toFloat()
    var radian = y * Math.PI / 180
    sin_mAngleY = Math.sin(radian).toFloat()
    cos_mAngleY = Math.cos(radian).toFloat()
    radian = z * Math.PI / 180
    sin_mAngleZ = Math.sin(radian).toFloat()
    cos_mAngleZ = Math.cos(radian).toFloat()
  }

  fun sortTagByScale() {
    Collections.sort(tagCloud, TagComparator())
  }

  fun update() {
    if (Math.abs(mAngleX) > 0.1 || Math.abs(mAngleY) > 0.1) {
      sineCosine(mAngleX, mAngleY, mAngleZ)
      updateAll()
    }
  }

  private fun updateAll() {
    val tagCloud = tagCloud ?: return
    for (i in tagCloud.indices) {
      var locX = tagCloud[i].locX
      var locY =
        tagCloud[i].locY * cos_mAngleX + tagCloud[i].locZ * -sin_mAngleX
      var locYa =
        tagCloud[i].locY * sin_mAngleX + tagCloud[i].locZ * cos_mAngleX
      var locXa = cos_mAngleY * locX + sin_mAngleY * locYa
      locX = locX * -sin_mAngleY + locYa * cos_mAngleY
      locYa = cos_mAngleZ * locXa + -sin_mAngleZ * locY
      locXa = locXa * sin_mAngleZ + locY * cos_mAngleZ
      tagCloud[i].locX = locYa
      tagCloud[i].locY = locXa
      tagCloud[i].locZ = locX
      locY = (radius * 2).toFloat()
      val te = locY / 1f / (locY + locX)
      tagCloud[i].loc2DX = locYa * te
      tagCloud[i].loc2DY = locXa * te
      tagCloud[i].scale = te
      tagCloud[i].setAlpha(te / 2f)
    }
    if (Build.VERSION.SDK_INT < 21) {
      sortTagByScale()
    }
  }

  companion object {
    private val DEFAULT_COLOR_DARK = floatArrayOf(0.886f, 0.725f, 0.188f, 1f)
    private val DEFAULT_COLOR_LIGHT = floatArrayOf(0.3f, 0.3f, 0.3f, 1f)
    private const val DEFAULT_RADIUS = 30
  }

  init {
    mAngleZ = 0f
    mAngleX = 0f
    mAngleY = 0f
    distrEven = true
    tagCloud = tags
    this.radius = radius
    this.tagColorLight = tagColorLight
    this.tagColorDark = tagColorDark
  }
}
