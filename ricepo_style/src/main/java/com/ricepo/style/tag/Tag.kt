package com.ricepo.style.tag

import android.graphics.Color
import android.view.View

class Tag @JvmOverloads constructor(
  var locX: Float = 0f,
  var locY: Float = 0f,
  var locZ: Float = 0f,
  var scale: Float = 1f,
  var popularity: Int = 5
) {
  private val argb: FloatArray
  var loc2DX = 0f
  var loc2DY = 0f
  var view: View? = null
//    var popularity: Int
//    private var scale: Float

//    constructor(locX: Float, locY: Float, locZ: Float, scale: Float) : this(
//        locX,
//        locY,
//        locZ,
//        scale,
//        5
//    ) {
//    }

  constructor(popularity: Int) : this(0f, 0f, 0f, 1f, popularity) {}

  val color: Int
    get() {
      val arr = IntArray(4)
      for (i in 0..3) {
        arr[i] = (argb[i] * 255f).toInt()
      }
      return Color.argb(arr[0], arr[1], arr[2], arr[3])
    }

  fun getScaleDiv(): Float {
    return scale / 2f
  }

  fun setAlpha(alpha: Float) {
    argb[0] = alpha
  }

  fun setColorByArray(arr: FloatArray?) {
    if (arr != null) {
      System.arraycopy(arr, 0, argb, argb.size - arr.size, arr.size)
    }
  }

  companion object {
    private const val DEFAULT_POPULARITY = 5
  }

  init {
    argb = floatArrayOf(1f, 0.5f, 0.5f, 0.5f)
  }
}
