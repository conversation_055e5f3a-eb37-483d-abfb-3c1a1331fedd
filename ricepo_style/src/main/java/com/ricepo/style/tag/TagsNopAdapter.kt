package com.ricepo.style.tag

import android.content.Context
import android.view.View
import android.view.ViewGroup

internal class TagsNopAdapter : TagsAdapter() {
  override val count: Int
    get() = 0

  override fun getItem(position: Int): Any? {
    return null
  }

  override fun getPopularity(popularity: Int): Int {
    return 0
  }

  override fun getView(
    context: Context?,
    position: Int,
    parent: ViewGroup?
  ): View? {
    return null
  }

  override fun onThemeColorChanged(child: View?, progress: Int) {}
}
