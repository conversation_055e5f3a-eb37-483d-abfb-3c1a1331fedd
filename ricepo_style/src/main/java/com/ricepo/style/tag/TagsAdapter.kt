package com.ricepo.style.tag

import android.content.Context
import android.view.View
import android.view.ViewGroup

abstract class TagsAdapter {
  interface OnDataSetChangeListener {
    fun onChange()
  }

  abstract val count: Int
  abstract fun getItem(position: Int): Any?
  abstract fun getPopularity(popularity: Int): Int
  abstract fun getView(context: Context?, position: Int, parent: ViewGroup?): View?

  abstract fun onThemeColorChanged(child: View?, progress: Int)

  private var onDataSetChangeListener: OnDataSetChangeListener? = null

  fun notifyDataSetChanged() {
    onDataSetChangeListener!!.onChange()
  }

  fun setOnDataSetChangeListener(listener: OnDataSetChangeListener?) {
    onDataSetChangeListener = listener
  }
}
