package com.ricepo.style.tag

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Point
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.VelocityTracker
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import com.ricepo.style.tag.TagsAdapter.OnDataSetChangeListener
import kotlin.math.sin

class TagCloudView : ViewGroup, Runnable, OnDataSetChangeListener {
  private val endHandler = Handler(Looper.getMainLooper())
  private val starHandler = Handler(Looper.getMainLooper())
  private val direction: TagPoint = TagPoint.make(4f, 2f, 0f)
  private var gestureDetector: GestureDetector? = null
  var isRun = true
  private var lastX = 0f
  private var lastY = 0f
  private val layoutParams: MarginLayoutParams? = null
  private var mTagCloud: TagCloud? = null
  private var mVelocityTracker: VelocityTracker? = null
  private var minSize = 0
  private var onTagClickListener: OnTagClickListener? = null
  private var tagsAdapter: TagsAdapter = TagsNopAdapter()
  private var velocity = 0.0
  private var tagPoints: ArrayList<TagPoint>? = null

  constructor(paramContext: Context) : super(paramContext) {
    init(paramContext, null)
  }

  constructor(paramContext: Context, paramAttributeSet: AttributeSet?) : super(
    paramContext,
    paramAttributeSet
  ) {
    init(paramContext, paramAttributeSet)
  }

  constructor(
    paramContext: Context,
    paramAttributeSet: AttributeSet?,
    paramInt: Int
  ) : super(paramContext, paramAttributeSet, paramInt) {
    init(paramContext, paramAttributeSet)
  }

  private fun addListener(paramView: View?, paramInt: Int) {
    if (!paramView!!.hasOnClickListeners() && onTagClickListener != null) {
      paramView.setOnClickListener { view -> onTagClickListener!!.onItemClick(this@TagCloudView, view, paramInt) }
    }
  }

  private fun addVelocityTrackerEvent(paramMotionEvent: MotionEvent) {
    if (mVelocityTracker == null) {
      mVelocityTracker = VelocityTracker.obtain()
    }
    mVelocityTracker!!.addMovement(paramMotionEvent)
  }

  private fun animateOpenView() {
    val localValueAnimator = ValueAnimator.ofFloat(0.0f, 1.0f)
    localValueAnimator.duration = 400L
    localValueAnimator.interpolator = DecelerateInterpolator()
    localValueAnimator.addUpdateListener { animator ->
      val f = animator.animatedValue as Float
      <EMAIL> = f
      <EMAIL> = f
    }
    localValueAnimator.start()
  }

  private fun autoTurnRotation() {
    val size = tagPoints!!.size
    for (i in 0 until size) {
      updateFrameOfPoint(i, direction, 0.002f)
    }
  }

  private val touchVelocityX: Int
    private get() {
      if (mVelocityTracker == null) {
        return 0
      }
      mVelocityTracker!!.computeCurrentVelocity(1000)
      return Math.abs(mVelocityTracker!!.xVelocity.toInt())
    }

  private val touchVelocityY: Int
    private get() {
      if (mVelocityTracker == null) {
        return 0
      }
      mVelocityTracker!!.computeCurrentVelocity(1000)
      return Math.abs(mVelocityTracker!!.yVelocity.toInt())
    }

  private fun inertiaStart() {
    timerStop()
    endHandler.post(this)
  }

  private fun inertiaStep() {
    if (java.lang.Double.isNaN(velocity)) {
      velocity = 5000.0
    }
    if (velocity <= 0.0f) {
      inertiaStop()
      return
    }
    velocity -= 120.0
    val f = (velocity / width * 16.0f / 1000.0f).toFloat()
    val size = tagPoints!!.size
    for (i in 0 until size) {
      updateFrameOfPoint(i, direction, f)
    }
  }

  private fun inertiaStop() {
    timerStart()
    endHandler.removeCallbacksAndMessages(null)
  }

  private fun init(
    paramContext: Context,
    paramAttributeSet: AttributeSet?
  ) {
    isFocusableInTouchMode = true
    mTagCloud = TagCloud()
    tagPoints = ArrayList()
    val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    val point = Point()
    wm.defaultDisplay.getSize(point)
    minSize = point.x
    if (point.y < point.x) {
      minSize = point.y
    }
  }

  private fun positionView(width: Int, point: TagPoint, child: View) {
    var width = width
    var x = point.x
    var halfWidth = width / 2.0f
    width = ((x + 1.0f) * halfWidth).toInt() - child.measuredWidth / 2
    val i = ((point.y + 1.0f) * halfWidth).toInt() - child.measuredHeight / 2
    child.layout(width, i, child.measuredWidth + width, child.measuredHeight + i)
    halfWidth = sin(point.z * Math.PI / 2.0).toFloat()
    x = halfWidth
    if (halfWidth < 0.1) {
      x = 0f
    }
    child.scaleX = x
    child.scaleY = x
    child.z = x
    child.alpha = x
  }

  private fun resetChildren() {
    val tagList = mTagCloud?.tagList ?: return
    removeAllViews()
    for (tag in tagList) {
      this.addView(tag.view)
    }
    tagPoints!!.clear()
    val childCount = this.childCount
    val factor = ((3 - Math.sqrt(5.0)) * Math.PI).toFloat()
    val halfCount = 2.0f / childCount
    for (index in 0 until childCount) {
      val ii = index * halfCount - 1f + halfCount / 2f
      val indexFactor = (index * factor).toDouble()
      val v11 = Math.sqrt(1f - ii * ii.toDouble())
      val point: TagPoint = TagPoint.Companion.make(
        (Math.cos(indexFactor) * v11).toFloat(),
        ii,
        (Math.sin(indexFactor) * v11).toFloat()
      )
      tagPoints!!.add(point)
      positionView(width, point, getChildAt(index))
    }
  }

  private fun setTagOfPoint(paramPoint: TagPoint, paramInt: Int) {
    val localView = getChildAt(paramInt)
    positionView(width, paramPoint, localView)
  }

  private fun timerStart() {
    isRun = true
  }

  private fun timerStop() {
    isRun = false
  }

  private fun updateFrameOfPoint(index: Int, point: TagPoint, angle: Float) {
    var point = point
    point = TagMatrix.pointRotation(tagPoints!![index], point, angle)
    tagPoints!![index] = point
    setTagOfPoint(point, index)
  }

  override fun dispatchTouchEvent(paramMotionEvent: MotionEvent): Boolean {
    parent.requestDisallowInterceptTouchEvent(true)
    return super.dispatchTouchEvent(paramMotionEvent)
  }

  override fun onAttachedToWindow() {
    super.onAttachedToWindow()
    starHandler.post(object : Runnable {
      override fun run() {
        starHandler.postDelayed(this, 16L)
        if (!isRun) {
          return
        }
        timerStart()
        autoTurnRotation()
      }
    })
    gestureDetector = GestureDetector(
      context,
      object : GestureDetector.OnGestureListener {
        override fun onDown(paramAnonymousMotionEvent: MotionEvent): Boolean {
          return false
        }

        override fun onFling(
          e1: MotionEvent?,
          e2: MotionEvent,
          velocityX: Float,
          velocityY: Float
        ): Boolean {
          velocity =
            Math.sqrt(velocityX * velocityX + velocityY * velocityY.toDouble())
          inertiaStart()
          timerStop()
          return false
        }

        override fun onLongPress(paramAnonymousMotionEvent: MotionEvent) {}
        override fun onScroll(
          paramAnonymousMotionEvent1: MotionEvent?,
          paramAnonymousMotionEvent2: MotionEvent,
          paramAnonymousFloat1: Float,
          paramAnonymousFloat2: Float
        ): Boolean {
          return false
        }

        override fun onShowPress(paramAnonymousMotionEvent: MotionEvent) {}
        override fun onSingleTapUp(paramAnonymousMotionEvent: MotionEvent): Boolean {
          return false
        }
      }
    )
  }

  override fun onChange() {
    mTagCloud!!.clear()
    val childCount = tagsAdapter.count
    for (i in 0 until childCount) {
      val localTag = Tag(tagsAdapter.getPopularity(i))
      val localView = tagsAdapter.getView(context, i, this)
      localTag.view = localView
      mTagCloud!!.add(localTag)
      addListener(localView, i)
    }
    resetChildren()
    animateOpenView()
  }

  override fun onDetachedFromWindow() {
    super.onDetachedFromWindow()
    isRun = false
    endHandler.removeCallbacksAndMessages(null)
    starHandler.removeCallbacksAndMessages(null)
    if (mVelocityTracker != null) {
      mVelocityTracker!!.recycle()
    }
  }

  override fun onInterceptTouchEvent(event: MotionEvent): Boolean {
    val x = event.x
    val y = event.y
    when (event.action) {
      MotionEvent.ACTION_MOVE -> {
        val lastX = lastX
        val lastY = lastY
        if (Math.abs(x - lastX) + Math.abs(y - lastY) > 10.0f) {
          return true
        }
      }
      MotionEvent.ACTION_DOWN -> {
        lastX = x
        lastY = y
      }
      else -> {
      }
    }
    return super.onInterceptTouchEvent(event)
  }

  override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
    tagPoints!!.clear()
    val childCount = childCount
    val factor = ((3.0 - Math.sqrt(5.0)) * Math.PI).toFloat()
    val halfChildCountReverse = (2.0 / childCount).toFloat()
    for (i in 0 until childCount) {
      val f1 = i * halfChildCountReverse - 1.0f + halfChildCountReverse / 2.0f
      val f2 = Math.sqrt(1.0f - f1 * f1.toDouble()).toFloat()
      val indexFactor = i * factor.toDouble()
      val indexFactorCos = Math.cos(indexFactor)
      val localPoint: TagPoint = TagPoint.Companion.make(
        (indexFactorCos * f2).toFloat(),
        f1,
        (Math.sin(indexFactor) * f2).toFloat()
      )
      tagPoints!!.add(localPoint)
      val localView = getChildAt(l)
      positionView(r, localPoint, localView)
    }
  }

  override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
    val widthSize = MeasureSpec.getSize(widthMeasureSpec)
    val childCount = this.childCount
    val widthRemainSize = widthSize - paddingLeft - paddingRight
    measureChildren(widthMeasureSpec, heightMeasureSpec)
    var countChildHeight = 0
    var lastChildHeight = 0
    var lastChildWith = 0
    var lastWidthSize = 0
    for (i in 0 until childCount) {
      val child = getChildAt(i)
      if (child != null && child.visibility != View.GONE) {
        if (child.measuredWidth + lastChildWith > widthRemainSize) {
          countChildHeight += lastChildHeight
          lastWidthSize = widthSize
          lastChildWith = child.measuredWidth
          lastChildHeight = child.measuredHeight
        } else {
          lastChildWith += child.measuredWidth
        }
        lastChildHeight = Math.max(lastChildHeight, child.measuredHeight)
      }
    }
    setMeasuredDimension(
      View.resolveSize(
        Math.max(
          lastChildWith,
          lastWidthSize
        ) + paddingLeft + paddingRight,
        widthMeasureSpec
      ),
      View.resolveSize(
        Math.max(
          countChildHeight + lastChildHeight,
          lastChildHeight
        ) + paddingBottom + paddingTop,
        heightMeasureSpec
      )
    )
  }

  override fun onTouchEvent(event: MotionEvent?): Boolean {
    val event = event ?: return false
    this.gestureDetector?.onTouchEvent(event)
    when (event.action) {
      MotionEvent.ACTION_MOVE -> {
        val x = event.x
        val y = event.y
        direction.x = lastY - y
        direction.y = x - lastX
        direction.z = 0.0f
        val ratio =
          Math.sqrt(direction.x * direction.x + direction.y * direction.y.toDouble()).toFloat() / width
        var i = 0
        while (i < tagPoints!!.size) {
          updateFrameOfPoint(i, direction, ratio * 2.0f)
          i++
        }
        lastX = x
        lastY = y
        return true
      }
      MotionEvent.ACTION_DOWN -> {
        timerStop()
        inertiaStop()
        lastX = event.x
        lastY = event.y
        return true
      }
      else -> {
      }
    }
    return true
  }

  fun onUserVisible(visible: Boolean) {
    if (visible) {
      isRun = false
      endHandler.removeCallbacksAndMessages(null)
      isRun = true
    } else {
      isRun = false
      endHandler.removeCallbacksAndMessages(null)
    }
  }

  override fun run() {
    endHandler.post(this)
    inertiaStep()
  }

  fun setAdapter(paramTagsAdapter: TagsAdapter) {
    tagsAdapter = paramTagsAdapter
    tagsAdapter.setOnDataSetChangeListener(this)
    onChange()
  }

  fun setOnTagClickListener(listener: OnTagClickListener?) {
    onTagClickListener = listener
  }

  interface OnTagClickListener {
    fun onItemClick(parent: ViewGroup?, child: View?, position: Int)
  }
}
