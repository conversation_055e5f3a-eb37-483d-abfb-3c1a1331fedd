package com.ricepo.style.span

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Paint.FontMetricsInt
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.text.style.ImageSpan
import java.lang.ref.WeakReference

//
// Created by Thomsen on 2021/9/1.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class CenteredImageSpan @JvmOverloads constructor(drawable: Drawable, verticalAlignment: Int = ALIGN_BOTTOM) :
  ImageSpan(
    drawable, verticalAlignment
  ) {
  // Extra variables used to redefine the Font Metrics when an ImageSpan is added
  private var initialDescent = 0
  private var extraSpace = 0

  override fun draw(
    canvas: Canvas,
    text: CharSequence,
    start: Int,
    end: Int,
    x: Float,
    top: Int,
    y: Int,
    bottom: Int,
    paint: Paint
  ) {
//        drawable.draw(canvas)

    val targetText = text.subSequence(start, end)
    val drawable = drawable
    canvas.save()
    val rect: Rect = drawable.bounds

    val fmPaint = paint.fontMetricsInt
    val fontHeight = fmPaint.descent - fmPaint.ascent
    val centerY = y + fmPaint.descent - fontHeight / 2
    val transY: Int = centerY - (rect.bottom - rect.top) / 2
    canvas.translate(x, transY.toFloat())
    drawable.draw(canvas)
    canvas.restore()
  }

  // Method used to redefined the Font Metrics when an ImageSpan is added
  override fun getSize(
    paint: Paint,
    text: CharSequence,
    start: Int,
    end: Int,
    fm: FontMetricsInt?
  ): Int {
    val d = cachedDrawable
    val rect = d?.bounds
    if (fm != null && rect != null) {
      // Centers the text with the ImageSpan
      if (rect.bottom - (fm.descent - fm.ascent) >= 0) {
        // Stores the initial descent and computes the margin available
        initialDescent = fm.descent
        extraSpace = rect.bottom - (fm.descent - fm.ascent)
      }
      fm.descent = extraSpace / 2 + initialDescent
      fm.bottom = fm.descent
      fm.ascent = -rect.bottom + fm.descent
      fm.top = fm.ascent
    }
    return rect?.right ?: 0
  }

  // Redefined locally because it is a private member from DynamicDrawableSpan
  private val cachedDrawable: Drawable?
    private get() {
      val wr = mDrawableRef
      var d: Drawable? = null
      if (wr != null) d = wr.get()
      if (d == null) {
        d = drawable
        mDrawableRef = WeakReference(d)
      }
      return d
    }
  private var mDrawableRef: WeakReference<Drawable?>? = null
}
