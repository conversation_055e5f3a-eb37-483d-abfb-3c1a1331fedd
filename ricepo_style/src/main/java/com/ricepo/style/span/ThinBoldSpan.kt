package com.ricepo.style.span

import android.content.Context
import android.graphics.Paint
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.style.CharacterStyle
import android.util.DisplayMetrics
import android.util.TypedValue
import com.ricepo.style.ResourceApplication

//
// Created by <PERSON><PERSON> on 18/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ThinBoldSpan(private val context: Context, private val thinSize: Float) : CharacterStyle() {

  override fun updateDrawState(tp: TextPaint) {
    tp.style = Paint.Style.FILL_AND_STROKE
    tp.strokeWidth = dpToPx(context, thinSize).toFloat()
  }

  private fun dpToPx(context: Context, dp: Float): Int {
    val metrics: DisplayMetrics = context.resources.displayMetrics
    return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, metrics).toInt()
  }

  companion object {
    fun getDefaultSpanString(s: String?): SpannableString {
      return getSpanString(ResourceApplication.context, s, 0.5f)
    }

    fun getDefaultSpanString(context: Context?, s: String?): SpannableString {
      return getSpanString(context, s, 0.5f)
    }

    fun getSpanString(context: Context?, s: String?, size: Float): SpannableString {
      if (context == null || s == null) {
        return SpannableString("")
      }
      val spannableString = SpannableString(s)
      val span = ThinBoldSpan(context, size)
      spannableString.setSpan(span, 0, s.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
      return spannableString
    }
  }
}
