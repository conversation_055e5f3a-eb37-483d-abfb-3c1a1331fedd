package com.ricepo.style.extension

import android.content.Context
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView

fun HorizontalLayout(
  context: Context,
  init: LinearLayout.() -> Unit,
): LinearLayout {
  return LinearLayout(context).apply {
    orientation = LinearLayout.HORIZONTAL
    init()
  }
}

fun VerticalLayout(
  context: Context,
  init: LinearLayout.() -> Unit,
): LinearLayout {
  return LinearLayout(context).apply {
    orientation = LinearLayout.VERTICAL
    init()
  }
}

fun ViewGroup.ImageView(
  index: Int? = null,
  init: (
    @ViewDslMarker ImageView
  ).() -> Unit
) {
  val image = ImageView(context).apply(init)
  if (index != null) {
    addView(image, index)
  } else {
    addView(image)
  }
}

fun ViewGroup.TextView(
  index: Int? = null,
  init: (@ViewDslMarker TextView).() -> Unit
) {
  val text = TextView(context).apply(init)
  if (index != null) {
    addView(text, index)
  } else {
    addView(text)
  }
}
