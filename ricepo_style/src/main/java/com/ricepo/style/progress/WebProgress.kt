package com.ricepo.style.progress

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import android.widget.FrameLayout
import androidx.annotation.Nullable

//
// Created by <PERSON><PERSON> on 3/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class WebProgress(context: Context, @Nullable attrs: AttributeSet?, defStyleAttr: Int) :
  FrameLayout(context, attrs, defStyleAttr) {

  private var mColor = 0

  private lateinit var mPaint: Paint

  private var mAnimator: Animator? = null

  private var mTargetWidth = 0

  private var mTargetHeight = 0

  /**
   * the progress status
   */
  private var STATUS = 0

  private var isShow = false
  private var mCurrentProgress = 0f

  constructor(context: Context) : this(context, null) {}
  constructor(context: Context, @Nullable attrs: AttributeSet?) : this(context, attrs, 0) {}

  private fun init(context: Context, attrs: AttributeSet?, defStyleAttr: Int) {
    mPaint = Paint()
    mColor = Color.parseColor(WEB_PROGRESS_COLOR)
    mPaint.setAntiAlias(true)
    mPaint.setColor(mColor)
    mPaint.setDither(true)
    mPaint.setStrokeCap(Paint.Cap.SQUARE)
    mTargetWidth = context.getResources().getDisplayMetrics().widthPixels
    mTargetHeight = dip2px(WEB_PROGRESS_DEFAULT_HEIGHT.toFloat())
  }

  fun setColor(color: Int) {
    mColor = color
    mPaint.setColor(color)
  }

  fun setColor(color: String?) {
    this.setColor(Color.parseColor(color))
  }

  fun setColor(startColor: Int, endColor: Int) {
    val linearGradient = LinearGradient(
      0f, 0f, mTargetWidth.toFloat(), mTargetHeight.toFloat(),
      startColor, endColor, Shader.TileMode.CLAMP
    )
    mPaint.setShader(linearGradient)
  }

  fun setColor(startColor: String?, endColor: String?) {
    this.setColor(Color.parseColor(startColor), Color.parseColor(endColor))
  }

  override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
    val wMode = MeasureSpec.getMode(widthMeasureSpec)
    var w = MeasureSpec.getSize(widthMeasureSpec)
    val hMode = MeasureSpec.getMode(heightMeasureSpec)
    var h = MeasureSpec.getSize(heightMeasureSpec)
    if (wMode == MeasureSpec.AT_MOST) {
      w = Math.min(w, context.resources.displayMetrics.widthPixels)
    }
    if (hMode == MeasureSpec.AT_MOST) {
      h = mTargetHeight
    }
    setMeasuredDimension(w, h)
  }

  override fun onDraw(canvas: Canvas) {}

  override fun dispatchDraw(canvas: Canvas) {
    canvas.drawRect(
      0f, 0f, mCurrentProgress / 100 * this.width.toFloat(),
      this.height.toFloat(), mPaint
    )
  }

  override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
    super.onSizeChanged(w, h, oldw, oldh)
    mTargetWidth = measuredWidth
    val screenWidth = context.resources.displayMetrics.widthPixels
    if (mTargetWidth >= screenWidth) {
      CURRENT_MAX_DECELERATE_SPEED_DURATION =
        MAX_DECELERATE_SPEED_DURATION
      CURRENT_MAX_UNIFORM_SPEED_DURATION = MAX_UNIFORM_SPEED_DURATION
    } else {
      val rate = mTargetWidth / screenWidth.toFloat()
      CURRENT_MAX_UNIFORM_SPEED_DURATION =
        (MAX_UNIFORM_SPEED_DURATION * rate).toInt()
      CURRENT_MAX_DECELERATE_SPEED_DURATION =
        (MAX_DECELERATE_SPEED_DURATION * rate).toInt()
    }
  }

  private fun setFinish() {
    isShow = false
    STATUS = FINISH
  }

  private fun startAnim(isFinished: Boolean) {
    val animator = mAnimator
    val v: Float = if (isFinished) 100f else 95.toFloat()
    if (animator != null && animator.isStarted()) {
      animator.cancel()
    }
    mCurrentProgress = if (mCurrentProgress == 0f) 0.00000001f else mCurrentProgress
    alpha = 1f
    if (!isFinished) {
      val mAnimator = ValueAnimator.ofFloat(mCurrentProgress, v)
      val residue = 1f - mCurrentProgress / 100 - 0.05f
      mAnimator.interpolator = LinearInterpolator()
      mAnimator.duration = (residue * CURRENT_MAX_UNIFORM_SPEED_DURATION).toLong()
      mAnimator.addUpdateListener(mAnimatorUpdateListener)
      mAnimator.start()
      this.mAnimator = mAnimator
    } else {
      var segment95Animator: ValueAnimator? = null
      if (mCurrentProgress < 95) {
        segment95Animator = ValueAnimator.ofFloat(mCurrentProgress, 95f)
        val residue = 1f - mCurrentProgress / 100f - 0.05f
        segment95Animator.interpolator = LinearInterpolator()
        segment95Animator.duration = (residue * CURRENT_MAX_DECELERATE_SPEED_DURATION).toLong()
        segment95Animator.interpolator = DecelerateInterpolator()
        segment95Animator.addUpdateListener(mAnimatorUpdateListener)
      }
      val mObjectAnimator = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f)
      mObjectAnimator.duration = DO_END_ALPHA_DURATION.toLong()
      val mValueAnimatorEnd = ValueAnimator.ofFloat(95f, 100f)
      mValueAnimatorEnd.duration = DO_END_PROGRESS_DURATION.toLong()
      mValueAnimatorEnd.addUpdateListener(mAnimatorUpdateListener)
      var mAnimatorSet = AnimatorSet()
      mAnimatorSet.playTogether(mObjectAnimator, mValueAnimatorEnd)
      if (segment95Animator != null) {
        val mAnimatorSet1 = AnimatorSet()
        mAnimatorSet1.play(mAnimatorSet).after(segment95Animator)
        mAnimatorSet = mAnimatorSet1
      }
      mAnimatorSet.addListener(mAnimatorListenerAdapter)
      mAnimatorSet.start()
      mAnimator = mAnimatorSet
    }
    STATUS = STARTED
  }

  private val mAnimatorUpdateListener =
    AnimatorUpdateListener { animation ->
      val t = animation.animatedValue as Float
      mCurrentProgress = t
      <EMAIL>()
    }
  private val mAnimatorListenerAdapter: AnimatorListenerAdapter = object : AnimatorListenerAdapter() {
    override fun onAnimationEnd(animation: Animator) {
      doEnd()
    }
  }

  override fun onDetachedFromWindow() {
    super.onDetachedFromWindow()
    /**
     * animator cause leak , if not cancel;
     */
    val animator = mAnimator
    if (animator != null && animator.isStarted()) {
      animator.cancel()
      mAnimator = null
    }
  }

  private fun doEnd() {
    if (STATUS == FINISH && mCurrentProgress == 100f) {
      visibility = View.GONE
      mCurrentProgress = 0f
      this.alpha = 1f
    }
    STATUS = UN_START
  }

  fun reset() {
    mCurrentProgress = 0f
    val animator = mAnimator
    if (animator != null && animator.isStarted()) {
      animator.cancel()
    }
  }

  fun setProgress(newProgress: Int) {
    setProgress(java.lang.Float.valueOf(newProgress.toFloat()))
  }

  fun offerLayoutParams(): LayoutParams {
    return LayoutParams(mTargetWidth, mTargetHeight)
  }

  private fun dip2px(dpValue: Float): Int {
    val scale = context.resources.displayMetrics.density
    return (dpValue * scale + 0.5f).toInt()
  }

  fun setHeight(heightDp: Int): WebProgress {
    mTargetHeight = dip2px(heightDp.toFloat())
    return this
  }

  fun setProgress(progress: Float) {
    if (STATUS == UN_START && progress == 100f) {
      visibility = View.GONE
      return
    }
    if (visibility == View.GONE) {
      visibility = View.VISIBLE
    }
    if (progress < 95) {
      return
    }
    if (STATUS != FINISH) {
      startAnim(true)
    }
  }

  /**
   * show progress bar
   */
  fun show() {
    isShow = true
    visibility = View.VISIBLE
    mCurrentProgress = 0f
    startAnim(false)
  }

  /**
   * hide progress bar
   */
  fun hide() {
    setWebProgress(100)
  }

  /**
   * show webview progress
   */
  fun setWebProgress(newProgress: Int) {
    if (newProgress >= 0 && newProgress < 95) {
      if (!isShow) {
        show()
      } else {
        setProgress(newProgress)
      }
    } else {
      setProgress(newProgress)
      setFinish()
    }
  }

  companion object {

    const val MAX_UNIFORM_SPEED_DURATION = 8 * 1000

    const val MAX_DECELERATE_SPEED_DURATION = 450

    /**
     * 95f-100f, the time of alpha 1f-0f
     */
    const val DO_END_ALPHA_DURATION = 630

    /**
     * 95f - 100f the time animator
     */
    const val DO_END_PROGRESS_DURATION = 500

    private var CURRENT_MAX_UNIFORM_SPEED_DURATION = MAX_UNIFORM_SPEED_DURATION

    private var CURRENT_MAX_DECELERATE_SPEED_DURATION = MAX_DECELERATE_SPEED_DURATION

    /**
     * default height progress bar
     */
    var WEB_PROGRESS_DEFAULT_HEIGHT = 3

    /**
     * default color progress bar
     */
    var WEB_PROGRESS_COLOR = "#2483D9"

    const val UN_START = 0
    const val STARTED = 1
    const val FINISH = 2
  }

  init {
    init(context, attrs, defStyleAttr)
  }
}
