package com.ricepo.style

import android.content.res.Configuration
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate

//
// Created by <PERSON><PERSON> on 26/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object ThemeUtil {

  var configChangeNightMode: Int = -1

  private val TAG = "ThemeUtil"

  /**
   * return boolean is the dart mode or light mode
   */
  fun isDarkMode(): Boolean {
    val mode = ResourceApplication.getResources().configuration.uiMode and
      Configuration.UI_MODE_NIGHT_MASK
    if (configChangeNightMode != -1) {
      return configChangeNightMode == Configuration.UI_MODE_NIGHT_YES
    }
    return mode == Configuration.UI_MODE_NIGHT_YES
  }

  fun changeThemeDark(newConfig: Configuration): Boolean {
    val currentNightMode = newConfig.uiMode and Configuration.UI_MODE_NIGHT_MASK
    Log.d(TAG, "onConfigurationChanged::$currentNightMode")
    val isChanged = (configChangeNightMode == currentNightMode)
    configChangeNightMode = currentNightMode
    when (currentNightMode) {
      Configuration.UI_MODE_NIGHT_NO -> {
        Log.d(TAG, "UI_MODE_NIGHT_NO")
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
      } // Night mode is not active, we're using the light theme
      Configuration.UI_MODE_NIGHT_YES -> {
        Log.d(TAG, "UI_MODE_NIGHT_YES")
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
      } // Night mode is active, we're using dark theme
    }
    return isChanged
  }
}
