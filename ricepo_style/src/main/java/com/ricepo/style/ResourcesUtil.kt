package com.ricepo.style

import android.content.Context
import android.content.res.Resources
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Build
import android.view.View
import androidx.annotation.ColorInt
import androidx.core.content.res.ResourcesCompat

//
// Created by <PERSON><PERSON> on 3/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

interface IResourcesUtil {
  fun getString(id: Int): String
}

object ResourcesUtil : IResourcesUtil {

  /**
   * get the font typeface
   */
  fun getFont(id: Int): Typeface? {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      ResourceApplication.getResources().getFont(id)
    } else {
      null
    }
  }

  /**
   * obtain resource color by diff theme
   */
  @Suppress("DEPRECATION")
  fun getColor(id: Int): Int {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      ResourceApplication.getResources().getColor(id, null)
    } else {
      ResourceApplication.getResources().getColor(id)
    }
  }

  /**
   * get the color by [id] and theme (dark mode)
   */
  @ColorInt
  fun getColor(id: Int, context: Context?): Int {
    val context = context ?: return getColor(id)
    return ResourcesCompat.getColor(context.resources, id, context.theme)
  }

  /**
   * get the color by [id] and theme (dark mode)
   */
  fun getColor(id: Int, view: View?): Int {
    val context = view?.context ?: return getColor(id)
    return ResourcesCompat.getColor(context.resources, id, context.theme)
  }

  /**
   * return color with alpha
   */
  fun getColorWithAlpha(alpha: Float, id: Int): Int {
    val a = Math.min(255, Math.max(0, alpha.times(255).toInt())) shl 24
    val rgb = 0x00ffffff and getColor(id)
    return a + rgb
  }

  /**
   * obtain resource dimen pixel int
   */
  fun getDimenPixelOffset(id: Int): Int {
    return ResourceApplication.getResources().getDimensionPixelOffset(id)
  }

  /**
   * obtain resource dimen pixel int by view context
   * sp / dp need * density
   * px not need
   */
  fun getDimenPixelOffset(view: View, id: Int): Int {
    return view.resources.getDimensionPixelOffset(id)
  }

  /**
   * obtain resource dimen pixel int by resources
   * sp / dp need * density
   * px not need
   */
  fun getDimenPixelOffset(resources: Resources?, id: Int): Int {
    val resources = resources ?: ResourceApplication.getResources()
    return resources.getDimensionPixelOffset(id)
  }

  /**
   * return int with resource with dimen pixel size
   * px also need * density
   */
  fun getDimenPixelSize(id: Int): Int {
    return ResourceApplication.getResources().getDimensionPixelSize(id)
  }

  /**
   * get the float value
   */
  fun getDimenSize(id: Int): Float {
    return ResourceApplication.getResources().getDimension(id)
  }

  /**
   * obtain resource string
   */
  override fun getString(id: Int): String {
    return ResourceApplication.getResources().getString(id)
  }

  /**
   * obtain resource string for one param
   */
  fun getString(id: Int, param: Any): String {
    return String.format(getString(id), param)
  }

  /**
   * obtain resource string for two param
   */
  fun getString(id: Int, param1: Any, param2: Any): String {
    return String.format(getString(id), param1, param2)
  }

  /**
   * obtain resource string for three param
   */
  fun getString(id: Int, param1: Any, param2: Any, param3: Any): String {
    return try {
      String.format(getString(id), param1, param2, param3)
    } catch (e: Exception) {
      getString(id)
    }
  }

  /**
   * return resource string by item [name]
   */
  fun getString(name: String): String {
    val resId = ResourceApplication.getResources().getIdentifier(
      name, "string",
      ResourceApplication.context.packageName
    )
    return if (resId != 0) getString(resId) else ""
  }

  /**
   * return resource string by item [name] and two params
   */
  fun getString(name: String, param1: Any, param2: Any): String {
    val resId = ResourceApplication.getResources().getIdentifier(
      name, "string",
      ResourceApplication.context.packageName
    )
    return if (resId != 0) getString(resId, param1, param2) else ""
  }

  /**
   * get the drawable by [id] and theme (dark mode)
   */
  fun getDrawable(id: Int, context: Context? = null): Drawable {
    val context = context ?: return ResourceApplication.getResources()
      .getDrawable(id, null)
    return ResourcesCompat.getDrawable(context.resources, id, context.theme)
      ?: ResourceApplication.getResources().getDrawable(id, null)
  }

  /**
   * get the drawable by [resName]
   */
  fun getDrawable(resName: String, context: Context? = null): Drawable? {
    val resId = getDrawableId(resName)

    if (resId == 0) {
      return null
    }

    return getDrawable(resId, context)
  }

  /**
   * get the drawable id by [resName]
   */
  fun getDrawableId(resName: String): Int {
    return ResourceApplication.getResources().getIdentifier(
      resName, "drawable",
      ResourceApplication.context.packageName
    )
  }
}
