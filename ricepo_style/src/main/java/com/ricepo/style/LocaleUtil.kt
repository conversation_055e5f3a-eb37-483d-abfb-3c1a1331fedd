package com.ricepo.style

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.LocaleList
import java.util.Locale

//
// Created by Thomsen on 26/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object LocaleUtil {

  /**
   * get current system language from application context
   */
  fun getResourcesLocale(): String {
    val locale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
      // don't use the getResources because maybe recursive
      ResourceApplication.context.resources.configuration.locales.get(0)
    } else {
      ResourceApplication.context.resources.configuration.locale
    }
    return if (!locale.country.isNullOrEmpty()) {
      "${locale.language}-${locale.country}"
    } else {
      "${locale.language}"
    }
  }

  fun getResourcesLanguage(): String {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
      val locales = ResourceApplication.getResources().configuration.locales
      val locale = locales.get(0)
      return locale.language
    } else {
      val locale = ResourceApplication.getResources().configuration.locale
      return locale.language
    }
  }

  /**
   * get the language map to locale const
   */
  fun getLanguageMapping(): String {
    val lang = getResourcesLocale()
    return when (lang) {
      LocaleConst.ENGLISH -> LocaleConst.enUS
      LocaleConst.zhHK, LocaleConst.zhTW -> LocaleConst.zhHK
      LocaleConst.ZH, LocaleConst.zhCN, LocaleConst.zhHans -> LocaleConst.zhCN
      LocaleConst.ES -> LocaleConst.ES
      else -> ""
    }
  }

  fun saveLanguage(context: Context, lang: String) {
    val pref = context.getSharedPreferences(LOCALE_PERF, Context.MODE_PRIVATE)
    with(pref.edit()) {
      putString(LOCALE_LANG, lang)
      commit()
    }
  }

  fun getLanguage(context: Context? = null): String? {
    val context = context ?: ResourceApplication.context
    val pref = context.getSharedPreferences(LOCALE_PERF, Context.MODE_PRIVATE)
    return pref.getString(LOCALE_LANG, null)
  }

  fun setLocale(context: Context): Context {
    val language = getLanguage(context) ?: getLanguageMapping()
    return updateResources(context, language)
  }

  fun changeApplicationLocale(context: Context, lang: String? = null): Context {
    val resources = context.applicationContext.resources
    val config = resources.configuration
    val language = lang ?: getLanguage(context)
    val locale = when (language) {
      LocaleConst.zhCN -> Locale.SIMPLIFIED_CHINESE
      LocaleConst.zhHK -> Locale.TRADITIONAL_CHINESE
      LocaleConst.enUS -> Locale.ENGLISH
      LocaleConst.ES -> Locale.forLanguageTag("es")
      else -> Locale.ENGLISH
    }
    config.setLocale(locale)
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
      val localeList = LocaleList(locale)
      LocaleList.setDefault(localeList)
      config.setLocales(localeList)
      context.applicationContext.createConfigurationContext(config)
    }
    return context.createConfigurationContext(config)
  }

  private fun updateResources(context: Context, language: String?): Context {
    val locale = when (language) {
      LocaleConst.zhCN -> Locale.SIMPLIFIED_CHINESE
      LocaleConst.zhHK -> Locale.TRADITIONAL_CHINESE
      LocaleConst.enUS -> Locale.ENGLISH
      LocaleConst.ES -> Locale.forLanguageTag("es")
      else -> Locale.ENGLISH
    }

    val resources = context.resources
    val config = Configuration(resources.configuration)
    config.setLocale(locale)
    return context.createConfigurationContext(config)
  }
}

private const val LOCALE_PERF = "locale_pref"

private const val LOCALE_LANG = "lang"

object LocaleConst {
  const val enUS = "en-US"
  const val zhHK = "zh-HK"
  const val zhTW = "zh-TW"
  const val zhCN = "zh-CN"
  const val zhHans = "zh-Hans"
  const val ZH = "zh"
  const val ES = "es"

  const val ENGLISH = "en"
}
