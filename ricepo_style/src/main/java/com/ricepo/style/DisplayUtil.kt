package com.ricepo.style

import android.content.Context
import android.content.res.Resources
import android.graphics.Point
import android.os.Build
import android.util.DisplayMetrics
import android.util.Log
import android.view.Display
import android.view.WindowManager
import com.google.gson.Gson
import com.ricepo.style.R
import java.lang.reflect.Field

//
// Created by <PERSON><PERSON> on 9/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
object DisplayUtil {

  /**
   * Get Display
   */
  private fun getDisplay(context: Context?): Display? {
    val wm: WindowManager?
    val context = context ?: ResourceApplication.context
    wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager?
    return wm?.defaultDisplay
  }

  /**
   * display metrics density
   */
  val density: Float =
    ResourceApplication.context.resources.displayMetrics.density

  /**
   * get screen with
   */
  fun getScreenHeight(context: Context? = null): Int {
    val display = getDisplay(context) ?: return 0
    val point = Point()
    display.getSize(point)

    return point.y
  }

  /**
   * content height = screenHeight - statusBarHeight - titleHeight
   */
  fun getContentHeight(): Int {
    val resources = ResourceApplication.context.resources
    val statusBarId = resources.getIdentifier(
      "status_bar_height",
      "dimen", "android"
    )
    var statusBarHeight = 0
    if (statusBarId > 0) {
      statusBarHeight = resources.getDimensionPixelOffset(statusBarId)
    }
    val titleHeight = dp2Px(50f).toInt()
    return getScreenHeight() - statusBarHeight - titleHeight
  }

  /**
   * get screen with
   */
  fun getScreenWidth(context: Context? = null): Int {
    val display = getDisplay(context) ?: return 0
    val point = Point()
    display.getSize(point)

    return point.x
  }

  /**
   * get the percent size of screen width
   */
  fun percentPx(context: Context?, percent: Float): Int {
    return getScreenWidth(context).times(percent).toInt()
  }

  fun px2Dp(px: Float): Float {
    val resources = ResourceApplication.context.resources
    return px / (resources.displayMetrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT)
  }

  fun dp2Px(dp: Float): Float {
    val resources = ResourceApplication.context.resources
    return dp * (resources.displayMetrics.densityDpi.toFloat() / DisplayMetrics.DENSITY_DEFAULT)
  }

  fun px2DpOffset(px: Float): Int {
    return px2Dp(px).toInt()
  }

  fun dp2PxOffset(dp: Float): Int {
    return dp2Px(dp).toInt()
  }

  fun sp2Px(context: Context, sp: Float): Int {
    val scale = context.resources.displayMetrics.scaledDensity
    return (sp * scale + 0.5f).toInt()
  }

  /**
   * activity and fragment and application need reset
   * otherwise density = width^2 + height^2
   * dpi 407 and with 1080
   * the density = 2.5
   * sw = 1080 / 2.5 = 432
   */
  fun setDensity(resources: Resources?) {
    val resources = resources ?: return
    try {
      // windowManager defaultDisplay realMetrics
      // ignore densityDpi = px^2 / size
      val metrics = resources.displayMetrics
      // No static field DENSITY_DEVICE_STABLE of 6.0.1 (Added in API level 24)
      if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
        try {
          val fields: Array<Field> = metrics::class.java.declaredFields
          val field = fields.firstOrNull { "noncompatDensityDpi".equals(it.name) }
          if (field != null) {
            field.isAccessible = true
            val dpi = field.get(metrics)
            if (dpi is Int) {
              metrics.densityDpi = dpi.toInt()
            }
          }
          // https://stackoverflow.com/questions/20631221/
          // why-lint-shows-warning-when-using-in-inch-or-mm-millimeter-units-as-dimensio/22707894#22707894
//                    metrics.densityDpi = metrics.xdpi.toInt()
          metrics.density = metrics.densityDpi / DisplayMetrics.DENSITY_DEFAULT.toFloat()
        } catch (e: java.lang.Exception) {
          metrics.densityDpi = DisplayMetrics.DENSITY_DEVICE_STABLE
          metrics.density =
            DisplayMetrics.DENSITY_DEVICE_STABLE / DisplayMetrics.DENSITY_DEFAULT.toFloat()
        }

        metrics.scaledDensity = metrics.density
        resources.displayMetrics.setTo(metrics)
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  /**
   * print density
   */
  fun logDensity(context: Context?) {
    val resources = context?.resources ?: return
    try {
      Log.i("thom", "metrics = ${Gson().toJson(resources.displayMetrics)}")
      Log.i("thom", "21% width = ${percentPx(context, 0.21f)}")
      // sw410 86.22 * 4 ( 1440 2872 5 rich 640dp 560dp)
      Log.i("thom", "sw_90 = ${ResourcesUtil.getDimenPixelOffset(R.dimen.sw_90dp)}")
      Log.i("thom", "sw_90 resource = ${resources.getDimension(R.dimen.sw_90dp)}")
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }
}
