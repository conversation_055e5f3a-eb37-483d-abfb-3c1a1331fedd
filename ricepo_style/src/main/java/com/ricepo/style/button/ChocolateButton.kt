package com.ricepo.style.button

import android.animation.IntEvaluator
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.isVisible
import com.ricepo.style.R
import com.ricepo.style.ResourcesUtil
import com.ricepo.style.databinding.ViewChocolateButtonBinding

//
// Created by <PERSON><PERSON> on 1/7/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class ChocolateButton : ConstraintLayout {

  constructor(context: Context) : super(context) {
    init(null)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(attrs)
    }

  private lateinit var binding: ViewChocolateButtonBinding

  /**
   * the max count to change button style
   */
  var maxCount = 0

  var currentCount = 0

  var isPlus: Boolean? = false
  var isMinus: Boolean? = false

  private val vipColor by lazy {
    resources.getColor(R.color.my, null)
  }
  private val mr by lazy {
    resources.getColor(R.color.mr, null)
  }
  private val mp by lazy {
    resources.getColor(R.color.mp, null)
  }

  private val white by lazy {
    resources.getColor(R.color.w, null)
  }

  private var isReward = false

  // init of view
  private fun init(attrs: AttributeSet?) {
    binding = ViewChocolateButtonBinding.inflate(
      LayoutInflater.from(context),
      this, true
    )
  }

  private fun syncState() {
    if (currentCount == 0) {
      binding.ivPlus.setImageResource(R.drawable.ic_add)
    } else {
      binding.ivPlus.setImageResource(R.drawable.ic_confirm)
    }
    if (!isReward) {
      if (currentCount == 0) {
        binding.root.setCardBackgroundColor(mp)
        binding.ivPlus.imageTintList = ColorStateList.valueOf(mr)
      } else {
        binding.root.setCardBackgroundColor(mr)
        binding.ivPlus.imageTintList = ColorStateList.valueOf(white)
      }
    }
  }

  private fun setText(text: String?) {
    binding.tvQtyNum.text = text
  }

  fun setEnable(enable: Boolean) {
    binding.ivSelectedMinus.isVisible = enable
    binding.ivSelectedPlus.isVisible = enable

    if (!enable) {
      val params = binding.tvQtyNum.layoutParams
      params.width = ResourcesUtil.getDimenPixelOffset(R.dimen.sw_28dp)
      params.height = ResourcesUtil.getDimenPixelOffset(R.dimen.sw_26dp)
      binding.tvQtyNum.layoutParams = params
    }
  }

  fun setReward(isReward: Boolean) {
    if (isReward) {
      setButtonGold()
    }
    this.isReward = isReward
  }

  private fun setButtonGold() {
    binding.root.setCardBackgroundColor(vipColor)
    binding.ivPlus.imageTintList = ColorStateList.valueOf(white)
    binding.ivSelectedMinus.setPadding(0, 0, 0, 0)
    binding.ivSelectedPlus.setPadding(0, 0, 0, 0)
  }

  fun setPlusSize(width: Int, height: Int) {
    val params = binding.ivPlus.layoutParams
    if (params is LayoutParams) {
      params.width = width
      params.height = height
      binding.ivPlus.layoutParams = params
    }
  }

  fun setMinusListener(minus: () -> Unit) {
    binding.ivSelectedMinus.setOnClickListener {
      if (isEnabled) {
        minus()
      }
    }
  }

  fun setPlusListener(plus: () -> Unit) {
    binding.ivSelectedPlus.setOnClickListener {
      if (isEnabled) {
        plus()
      }
    }
    binding.ivPlus.setOnClickListener {
      if (isEnabled) {
        plus()
      }
    }
  }

  fun setSelectedCount(count: Int) {
    currentCount = count
    if (maxCount == 1) {
      setQtyNumVisible(count > 0)
      setQtyAddVisible(count < 1)
    } else {
      when (count) {
        1 -> {
          expand()
        }
        0 -> {
          systolic()
        }
        else -> {
          setQtyNumVisible(count > 0)
          setQtyAddVisible(count < 1)
        }
      }
    }
    if (count > 0) {
      setText(count.toString())
    }
    syncState()
  }

  fun expand(
    onStart: (() -> Unit)? = null,
    onEnd: (() -> Unit)? = null,
  ) {
    binding.layQtyNum.isVisible = true
    setQtyAddVisible(false)
    if (isPlus == true) else return
    binding.layQtyNum.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
    val widthEnd = binding.layQtyNum.measuredWidth
    binding.layQtyAdd.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
    val widthStart = binding.layQtyAdd.measuredWidth
    widthAnimate(binding.layQtyNum, widthStart, widthEnd, onStart, onEnd)
  }

  fun systolic(
    onStart: (() -> Unit)? = null,
    onEnd: (() -> Unit)? = null,
  ) {
    if (isMinus == true) else {
      binding.layQtyNum.isVisible = false
      setQtyAddVisible(true)
      setText(null)
      return
    }
    binding.layQtyNum.isVisible = true
    binding.layQtyNum.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
    val widthEnd = binding.layQtyNum.measuredWidth
    binding.layQtyAdd.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED)
    val widthStart = binding.layQtyAdd.measuredWidth
    widthAnimate(binding.layQtyNum, widthEnd, widthStart, onStart, onEnd)
    binding.layQtyAdd.postDelayed(
      {
        binding.layQtyNum.isVisible = false
        setQtyAddVisible(true)
        setText(null)
      },
      DURATION
    )
  }

  private fun setQtyNumVisible(isVisible: Boolean) {
    if (maxCount == 1) {
      binding.layQtyNum.isVisible = false
    } else {
      binding.layQtyNum.isVisible = isVisible
    }
  }

  private fun setQtyAddVisible(isVisible: Boolean) {
    if (maxCount == 1) {
      binding.layQtyAdd.isVisible = true
    } else {
      binding.layQtyAdd.isVisible = isVisible
    }
  }

  fun setQtyAddGravity(gravity: Int) {
    val addParams = binding.layQtyAdd.layoutParams
    if (addParams is FrameLayout.LayoutParams) {
      addParams.gravity = gravity
      binding.layQtyAdd.layoutParams = addParams
    }

    if (gravity == Gravity.LEFT) {
      binding.ivPlus.scaleType = ImageView.ScaleType.FIT_START
    } else if (gravity == Gravity.RIGHT) {
      binding.ivPlus.scaleType = ImageView.ScaleType.FIT_END
    }
  }

  private fun widthAnimate(
    target: View,
    start: Int,
    end: Int,
    onStart: (() -> Unit)? = null,
    onEnd: (() -> Unit)? = null,
  ) {
    ValueAnimator.ofInt(1, 100).apply {
      addUpdateListener(object : AnimatorUpdateListener {
        private val mIntEvaluator = IntEvaluator()
        override fun onAnimationUpdate(animation: ValueAnimator) {
          val fraction = animation.animatedFraction
          val width = mIntEvaluator.evaluate(fraction, start, end)
          target.layoutParams.width = width
          target.requestLayout()
        }
      })
      duration = DURATION
      doOnStart {
        onStart?.invoke()
      }
      doOnEnd {
        onEnd?.invoke()
      }
    }.start()
  }

  private val DURATION: Long = 150
}
