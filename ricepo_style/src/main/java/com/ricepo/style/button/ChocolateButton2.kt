package com.ricepo.style.button

import android.animation.LayoutTransition
import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.google.android.material.card.MaterialCardView
import com.ricepo.style.R
import com.ricepo.style.extension.HorizontalLayout
import com.ricepo.style.view.dp

/**
 *  because the shit coupling not use this widget currently
 */

class ChocolateButton2 : MaterialCardView {

  constructor(context: Context) : super(context)

  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

  private val rootView: LinearLayout

  var maxCount = 0

  var currentCount = 0
    set(value) {
      field = value
      setSelectedCount(value)
    }

  private val tvSize = 8.dp.toFloat()

  var isPlus: Boolean? = false
  var isMinus: Boolean? = false

  private val plusView by lazy {
    TextView(context).apply {
      text = "+"
      textSize = tvSize
    }
  }

  private val minusView by lazy {
    TextView(context).apply {
      text = "-"
      textSize = tvSize
    }
  }

  private val selectView by lazy {
    AppCompatImageView(context).apply {
      setImageResource(R.drawable.ic_confirm)
      imageTintList = ColorStateList.valueOf(
        resources.getColor(R.color.w, null)
      )
      setPadding(0, 8.dp, 0, 8.dp)
    }
  }

  private val countView by lazy {
    TextView(context).apply {
      text = currentCount.toString()
      textSize = tvSize
      setPadding(12.dp, 0, 12.dp, 0)
    }
  }

  init {
    addView(
      HorizontalLayout(context) {
        layoutTransition = LayoutTransition().apply {
          enableTransitionType(LayoutTransition.CHANGING)
        }
        addView(plusView)
      }.also {
        rootView = it
      }
    )
    radius = 8.dp.toFloat()
    val padding = 8.dp
    setContentPadding(padding, 0, padding, 0)
  }

  fun scale(
    isExpand: Boolean
  ) {
    if (isExpand) {
      add()
    } else {
      remove()
    }
  }

  fun setMinusListener(minus: () -> Unit) {
    minusView.setOnClickListener {
      if (isEnabled) {
//        minus()
        setSelectedCount(--currentCount)
      }
    }
  }

  fun setPlusListener(plus: () -> Unit) {
    plusView.setOnClickListener {
      if (isEnabled) {
//        plus()
        setSelectedCount(++currentCount)
      }
    }
  }

  fun setSelectedCount(count: Int) {
//    currentCount = count
    if (maxCount == 1) {
      if (count > 0) {
        checked()
      } else {
        unchecked()
      }
    } else {
      when (count) {
        1 -> {
          add()
        }
        0 -> {
          remove()
        }
        else -> {}
      }
    }
    if (count > 0) {
      countView.text = count.toString()
    }
  }

  fun setReward(isReward: Boolean) {
    if (isReward) {
//      setButtonGold()
    } else {
//      setButtonSelected()
    }
  }

  fun setEnable(enable: Boolean) {
//    binding.ivSelectedMinus.isVisible = enable
//    binding.ivSelectedPlus.isVisible = enable
//
//    if (!enable) {
//      val params = binding.tvQtyNum.layoutParams
//      params.width = ResourcesUtil.getDimenPixelOffset(R.dimen.sw_28dp)
//      params.height = ResourcesUtil.getDimenPixelOffset(R.dimen.sw_26dp)
//      binding.tvQtyNum.layoutParams = params
//    }
  }

  private fun checked() {
    rootView.removeAllViews()
    rootView.addView(selectView)
  }

  private fun unchecked() {
    rootView.removeAllViews()
    rootView.addView(plusView)
  }

  private fun remove() {
    rootView.removeView(minusView)
    rootView.removeView(countView)
  }

  private fun add() {
    remove()
    rootView.apply {
      addView(minusView, 0)
      addView(countView, 1)
    }
  }
}
