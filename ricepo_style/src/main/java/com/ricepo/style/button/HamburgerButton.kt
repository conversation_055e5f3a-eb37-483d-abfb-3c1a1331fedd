package com.ricepo.style.button

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.util.AttributeSet
import android.view.animation.DecelerateInterpolator
import androidx.appcompat.widget.AppCompatButton
import kotlin.properties.Delegates

//
// Created by <PERSON><PERSON> on 11/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class HamburgerButton : AppCompatButton {

  constructor(context: Context) : super(context) {
    init(null)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(attrs)
    }

  private val ROTATION_ANGLE: Float = 45f

  private var lineLength by Delegates.notNull<Float>()

  // the global of paint
  private lateinit var paint: Paint

  // the rotate value of status canvas
  private var rotateAngle: Float = 0f

  // the enable of rotate line
  private var isEnable: Boolean = true

  // init the paint
  private fun initPaint(attrs: AttributeSet?) {
    lineLength = 8 * resources.displayMetrics.density

    paint = Paint()
    paint.isAntiAlias = true

    paint.color = currentTextColor

    paint.style = Paint.Style.FILL

    // default paint width
    paint.strokeWidth = 1f * resources.displayMetrics.density
  }

  // init of view
  private fun init(attrs: AttributeSet?) {
    initPaint(attrs)
  }

  override fun onDraw(canvas: Canvas) {
    if (isEnable) {
      drawText(canvas)
      drawLine(canvas)
    }

    super.onDraw(canvas)
  }

  /**
   * start the rotate animation with rotate [type]
   * tag last rotate type
   */
  fun startRotate(type: RotateType, tag: RotateType?) {
    when (type) {
      RotateType.Cross -> {
        if (tag == RotateType.Minus) {
          // from two to one
          startRotate(false)
        } else {
          rotateAngle = 0f
          invalidate()
        }
      }
      RotateType.Minus -> {
        if (tag == RotateType.Cross) {
          // from one to two
          startRotate(true)
        } else {
          rotateAngle = ROTATION_ANGLE
          invalidate()
        }
      }
    }
  }

  private fun startRotate(isPositive: Boolean) {

    var startAngle = ROTATION_ANGLE
    var endAngle = 0f

    // from one to two
    if (isPositive) {
      startAngle = 0f
      endAngle = ROTATION_ANGLE
    }

    val animator = ValueAnimator.ofFloat(startAngle, endAngle)
    animator.duration = 400
    animator.interpolator = DecelerateInterpolator()
    animator.addUpdateListener {
      rotateAngle = it.animatedValue as Float
      invalidate()
    }
    animator.start()
  }

  /**
   * draw the line of status flag
   */
  private fun drawLine(canvas: Canvas?) {
    val xStart: Float = measuredWidth.minus(lineLength)
    val yStart: Float = 0f
    val xEnd: Float = measuredWidth.toFloat()
    val yEnd: Float = measuredHeight.toFloat()

    // save the canvas layer
    val layerId: Int = canvas?.saveLayer(xStart, 0f, xEnd, measuredHeight.toFloat(), paint) ?: 0

    val bPaint = Paint()
    bPaint.isAntiAlias = true
    bPaint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

    val bmpTop: Bitmap? = makeTopLayer(lineLength.toInt(), measuredHeight)
    if (bmpTop != null) {
      canvas?.drawBitmap(
        bmpTop, Rect(0, 0, bmpTop.width, bmpTop.height),
        Rect(xStart.toInt(), yStart.toInt(), xEnd.toInt(), yEnd.toInt()), paint
      )
    }

    val bmpBottom: Bitmap? = makeBottomLayer(lineLength.toInt(), measuredHeight)
    if (bmpBottom != null) {
      canvas?.drawBitmap(
        bmpBottom, Rect(0, 0, bmpBottom.width, bmpBottom.height),
        Rect(xStart.toInt(), yStart.toInt(), xEnd.toInt(), yEnd.toInt()), paint
      )
    }

    // restore the canvas
    canvas?.restoreToCount(layerId)
  }

  /**
   * from bottom to top draw line
   */
  private fun makeBottomLayer(w: Int, h: Int): Bitmap? {
    if (w == 0 || h == 0) return null
    val bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
    val c = Canvas(bitmap)

    val halfHeight = height.div(2f)
//        val sinValue = sin(rotateAngle.toDouble()).times(LINE_LENGTH).toFloat()
    // LINE_LENGTH / 2 * 1.414
    val sinValue = 4 * resources.displayMetrics.density
    val yStart = halfHeight.plus(sinValue)
    val yEnd = halfHeight.minus(sinValue)

    c.rotate(rotateAngle, w / 2f, h / 2f)

    c.drawLine(0f, yStart, lineLength, yEnd, paint)

    return bitmap
  }

  /**
   * from top to bottom draw line
   */
  private fun makeTopLayer(w: Int, h: Int): Bitmap? {
    if (w == 0 || h == 0) return null
    val bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
    val c = Canvas(bitmap)

    val halfHeight = height.div(2f)
//        val sinValue = sin(rotateAngle.toDouble()).times(LINE_LENGTH).toFloat()
    val sinValue = 4 * resources.displayMetrics.density
    val yStart = halfHeight.minus(sinValue)
    val yEnd = halfHeight.plus(sinValue)

    c.rotate(-rotateAngle, w / 2f, h / 2f)

    c.drawLine(0f, yStart, lineLength, yEnd, paint)

    return bitmap
  }

  /**
   * draw the content text
   */
  private fun drawText(canvas: Canvas?) {
    val offset = measuredWidth / 2 - textSize
    canvas?.translate(-offset, 0f)
  }

  fun setEnable(enable: Boolean) {
    this.isEnable = enable
  }

  enum class RotateType {
    Cross,
    Minus,
  }
}
