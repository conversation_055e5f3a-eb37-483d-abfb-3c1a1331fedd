package com.ricepo.style.font

import android.graphics.Typeface
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.widget.TextView
import androidx.core.provider.FontRequest
import androidx.core.provider.FontsContractCompat
import com.ricepo.style.R
import java.lang.StringBuilder

//
// Created by <PERSON><PERSON> on 9/9/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

object FontFacade {

  /**
   * set the text view typeface
   */
  fun setTypeface(textView: TextView) {
    val fontRequest = FontRequest(
      "com.google.android.gms.fonts",
      "com.google.android.gms",
      buildFontQuery("Source Sans Pro", 600),
      R.array.com_google_android_gms_fonts_certs
    )

    val fontCallback = object : FontsContractCompat.FontRequestCallback() {
      override fun onTypefaceRetrieved(typeface: Typeface?) {
        textView.typeface = typeface
      }

      override fun onTypefaceRequestFailed(reason: Int) {
        Log.w("thom", "FontFacade failed $reason")
      }
    }

    val handlerThread = HandlerThread("fonts")
    handlerThread.start()
    FontsContractCompat.requestFont(
      textView.context, fontRequest,
      fontCallback, Handler(handlerThread.looper)
    )
  }

  private fun buildFontQuery(familyName: String, weight: Int?): String {
    val builder = StringBuilder()
    builder.append("name=$familyName")
    weight?.let {
      builder.append("&weight=$weight")
    }
//        builder.append("&width=100.0&italic=0.0&besteffort=true")
    return builder.toString()
  }
}
