
package com.ricepo.style.round

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.Checkable
import androidx.constraintlayout.widget.ConstraintLayout
import com.ricepo.style.round.helper.RoundCornerAtts
import com.ricepo.style.round.helper.RoundCornerHelper

/**
 */
class RoundLayout @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), Checkable, RoundCornerAtts {

  private var mRCHelper: RoundCornerHelper = RoundCornerHelper()

  override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
    super.onSizeChanged(w, h, oldw, oldh)
    mRCHelper.onSizeChanged(this, w, h)
  }

  override fun dispatchDraw(canvas: Canvas) {
    canvas.saveLayer(mRCHelper.mLayer, null)
    super.dispatchDraw(canvas)
    mRCHelper.onClipDraw(canvas)
    canvas.restore()
  }

  override fun draw(canvas: Canvas) {
    if (mRCHelper.mClipBackground) {
      canvas.save()
      canvas.clipPath(mRCHelper.mClipPath!!)
      super.draw(canvas)
      canvas.restore()
    } else {
      super.draw(canvas)
    }
  }

  override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
    val action = ev.action
    if (action == MotionEvent.ACTION_DOWN && !mRCHelper.mAreaRegion!!.contains(
        ev.x.toInt(),
        ev.y.toInt()
      )
    ) {
      return false
    }
    if (action == MotionEvent.ACTION_DOWN || action == MotionEvent.ACTION_UP) {
      refreshDrawableState()
    } else if (action == MotionEvent.ACTION_CANCEL) {
      isPressed = false
      refreshDrawableState()
    }
    return super.dispatchTouchEvent(ev)
  }

  override fun setRadius(radius: Int) {
    for (i in mRCHelper.radii.indices) {
      mRCHelper.radii[i] = radius.toFloat()
    }
    invalidate()
  }

  override fun setTopLeftRadius(topLeftRadius: Int) {
    mRCHelper.radii[0] = topLeftRadius.toFloat()
    mRCHelper.radii[1] = topLeftRadius.toFloat()
    invalidate()
  }

  override fun setTopRightRadius(topRightRadius: Int) {
    mRCHelper.radii[2] = topRightRadius.toFloat()
    mRCHelper.radii[3] = topRightRadius.toFloat()
    invalidate()
  }

  override fun setBottomLeftRadius(bottomLeftRadius: Int) {
    mRCHelper.radii[6] = bottomLeftRadius.toFloat()
    mRCHelper.radii[7] = bottomLeftRadius.toFloat()
    invalidate()
  }

  override fun setBottomRightRadius(bottomRightRadius: Int) {
    mRCHelper.radii[4] = bottomRightRadius.toFloat()
    mRCHelper.radii[5] = bottomRightRadius.toFloat()
    invalidate()
  }

  override fun invalidate() {
    if (null != mRCHelper) mRCHelper.refreshRegion(this)
    super.invalidate()
  }

  // --- public interface -----------------------------------------------------------------------
  override var isClipBackground: Boolean
    get() = mRCHelper.mClipBackground
    set(clipBackground) {
      mRCHelper.mClipBackground = clipBackground
      invalidate()
    }

  override var isRoundAsCircle: Boolean
    get() = mRCHelper.mRoundAsCircle
    set(roundAsCircle) {
      mRCHelper.mRoundAsCircle = roundAsCircle
      invalidate()
    }

  override fun getTopLeftRadius(): Float {
    return mRCHelper.radii[0]
  }

  override fun getTopRightRadius(): Float {
    return mRCHelper.radii[2]
  }

  override fun getBottomLeftRadius(): Float {
    return mRCHelper.radii[4]
  }

  override fun getBottomRightRadius(): Float {
    return mRCHelper.radii[6]
  }

  override var strokeWidth: Int
    get() = mRCHelper.mStrokeWidth
    set(strokeWidth) {
      mRCHelper.mStrokeWidth = strokeWidth
      invalidate()
    }

  override var strokeColor: Int
    get() = mRCHelper.mStrokeColor
    set(strokeColor) {
      mRCHelper.mStrokeColor = strokeColor
      invalidate()
    }

  // --- Selector support -----------------------------------------------------------------------
  override fun drawableStateChanged() {
    super.drawableStateChanged()
    mRCHelper.drawableStateChanged(this)
  }

  override fun setChecked(checked: Boolean) {
    if (mRCHelper.mChecked != checked) {
      mRCHelper.mChecked = checked
      refreshDrawableState()
      if (mRCHelper.mOnCheckedChangeListener != null) {
        mRCHelper.mOnCheckedChangeListener!!.onCheckedChanged(this, mRCHelper.mChecked)
      }
    }
  }

  override fun isChecked(): Boolean {
    return mRCHelper.mChecked
  }

  override fun toggle() {
    isChecked = !mRCHelper.mChecked
  }

  fun setOnCheckedChangeListener(listener: RoundCornerHelper.OnCheckedChangeListener?) {
    mRCHelper.mOnCheckedChangeListener = listener
  }

  init {
    mRCHelper.initAttrs(context, attrs)
  }
}
