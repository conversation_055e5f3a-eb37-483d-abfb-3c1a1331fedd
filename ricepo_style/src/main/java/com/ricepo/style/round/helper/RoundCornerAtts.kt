
package com.ricepo.style.round.helper

interface RoundCornerAtts {
  fun setRadius(radius: Int)
  fun setTopLeftRadius(topLeftRadius: Int)
  fun setTopRightRadius(topRightRadius: Int)
  fun setBottomLeftRadius(bottomLeftRadius: Int)
  fun setBottomRightRadius(bottomRightRadius: Int)
  var isClipBackground: Boolean
  var isRoundAsCircle: Boolean
  fun getTopLeftRadius(): Float
  fun getTopRightRadius(): Float
  fun getBottomLeftRadius(): Float
  fun getBottomRightRadius(): Float
  var strokeWidth: Int
  var strokeColor: Int
}
