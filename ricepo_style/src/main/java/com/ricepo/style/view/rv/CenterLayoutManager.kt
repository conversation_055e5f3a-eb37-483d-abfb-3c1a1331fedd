package com.ricepo.style.view.rv

import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

//
// Created by <PERSON><PERSON> on 12/15/20.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class CenterLayoutManager : LinearLayoutManager {

  private lateinit var context: Context

  constructor(context: Context) : super(context) {
    this.context = context
  }

  constructor(context: Context, orientation: Int, reverseLayout: Boolean) : super(
    context,
    orientation,
    reverseLayout
  ) {
    this.context = context
  }

  constructor(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int,
    defStyleRes: Int
  ) : super(context, attrs, defStyleAttr, defStyleRes) {
    this.context = context
  }

  override fun scrollToPosition(position: Int) {
    scrollToCenter(context, position)
  }

  override fun smoothScrollToPosition(
    recyclerView: RecyclerView,
    state: RecyclerView.State?,
    position: Int
  ) {
    scrollSmoothToCenter(context, position)
  }

  private fun scrollToCenter(context: Context, position: Int) {
    val scroller = CenterSmoothScroller(context, 1f)
    scroller.targetPosition = position
    startSmoothScroll(scroller)
  }

  private fun scrollSmoothToCenter(context: Context, position: Int) {
    val scroller = CenterSmoothScroller(context, 5f)
    scroller.targetPosition = position
    startSmoothScroll(scroller)
  }
}
