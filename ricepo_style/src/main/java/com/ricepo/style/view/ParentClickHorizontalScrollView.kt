package com.ricepo.style.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.HorizontalScrollView

//
// Created by <PERSON><PERSON> on 28/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class ParentClickHorizontalScrollView : HorizontalScrollView {
  private var parentView: View? = null

  constructor(context: Context?) : super(context) {}
  constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {}
  constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {}

  fun getParentView(): View? {
    return parentView
  }

  fun setParentView(parentView: View?) {
    this.parentView = parentView
  }

  override fun onTouchEvent(ev: MotionEvent): Boolean {
    if (parentView == null && parent != null && parent is View) {
      parentView = parent as View
    }
    if (parentView != null) {
      when (ev.action) {
        MotionEvent.ACTION_DOWN -> parentView?.onTouchEvent(ev)
        MotionEvent.ACTION_MOVE -> {
          ev.action = MotionEvent.ACTION_CANCEL
          parentView?.onTouchEvent(ev)
          ev.action = MotionEvent.ACTION_MOVE
        }
        MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> parentView?.onTouchEvent(ev)
      }
    }
    return super.onTouchEvent(ev)
  }
}
