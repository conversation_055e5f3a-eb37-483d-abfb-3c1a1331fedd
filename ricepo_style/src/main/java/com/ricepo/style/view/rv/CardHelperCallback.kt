package com.ricepo.style.view.rv

import android.graphics.Canvas
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.abs

//
// Created by <PERSON><PERSON> on 4/19/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class CardHelperCallback : ItemTouchHelper.Callback() {
  private var mListener: OnItemTouchCallbackListener? = null
  fun setListener(listener: OnItemTouchCallbackListener?) {
    mListener = listener
  }

  override fun getMovementFlags(
    recyclerView: RecyclerView,
    viewHolder: RecyclerView.ViewHolder
  ): Int {
    val dragFlags = 0
    var swipeFlags = 0
    val layoutManager = recyclerView.layoutManager
    if (layoutManager is CardManager) {
      // allowed sliding direction
      swipeFlags = ItemTouchHelper.LEFT
    }
    return makeMovementFlags(dragFlags, swipeFlags)
  }

  override fun onMove(
    recyclerView: RecyclerView,
    viewHolder: RecyclerView.ViewHolder,
    target: RecyclerView.ViewHolder
  ): Boolean {
    return false
  }

  // itemView swipe out of screen
  override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
    if (mListener != null) {
      mListener!!.onSwiped(viewHolder.adapterPosition, direction)
    }
  }

  /**
   * whether the drag-and-drop operation is performed when long pressed
   * @return
   */
  override fun isLongPressDragEnabled(): Boolean {
    return false
  }

  /**
   * whether or not it can be slid
   * @return
   */
  override fun isItemViewSwipeEnabled(): Boolean {
    return true
  }

  override fun onChildDraw(
    c: Canvas,
    recyclerView: RecyclerView,
    viewHolder: RecyclerView.ViewHolder,
    dX: Float,
    dY: Float,
    actionState: Int,
    isCurrentlyActive: Boolean
  ) {
    super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
    // the direction with the larger offset is used as the standard
    val trans = if (abs(dX) > abs(dY)) {
      abs(dX)
    } else {
      abs(dY)
    }
    // the ratio of swipe
    var ratio = trans / getThreshold(recyclerView, viewHolder)
    if (ratio > 1) {
      ratio = 1f
    }
    // the amount of itemView
    val itemCount = recyclerView.childCount
    if (itemCount < CardManager.MAX_COUNT) return

    // add animation for the View shown at the bottom when removed
    for (i in 1 until CardManager.MAX_COUNT - 1) {
      val view = recyclerView.getChildAt(i)
      val t = 1 / (1 - CardManager.SCALE_RATIO * ratio) - CardManager.SCALE_RATIO * (itemCount - i - 1)
      view?.scaleY = t
      view?.translationX = -CardManager.TRANS_RATIO * ratio + CardManager.TRANS_RATIO * (itemCount - i - 1)
    }
    // the current drag view animation
//        val view = recyclerView.getChildAt(itemCount - 1)
//        view?.alpha = 1 - Math.abs(ratio) * 0.2f
  }

  // get the distance threshold for scribing out of the screen
  private fun getThreshold(
    recyclerView: RecyclerView,
    viewHolder: RecyclerView.ViewHolder
  ): Float {
    return recyclerView.width * getSwipeThreshold(viewHolder)
  }

  interface OnItemTouchCallbackListener {
    // callback method for item being slid
    fun onSwiped(position: Int, direction: Int)
  }
}
