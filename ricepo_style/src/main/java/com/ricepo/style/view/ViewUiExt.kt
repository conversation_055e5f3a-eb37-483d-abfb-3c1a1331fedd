package com.ricepo.style.view

import android.content.res.Resources
import android.view.Gravity
import android.view.View
import android.view.ViewAnimationUtils
import android.view.ViewGroup
import androidx.core.animation.doOnEnd
import androidx.core.view.forEach
import androidx.transition.Fade
import androidx.transition.Slide
import androidx.transition.Transition
import androidx.transition.TransitionManager
import kotlin.math.hypot

fun View.setEnableForeground(
  enabled: Boolean,
) {
  if (this is ViewGroup) {
    forEach {
      it.alpha = if (enabled) 1f else 0.2f
    }
  } else {
    alpha = if (enabled) 1f else 0.2f
  }
}

fun View.getColor(int: Int) = resources.getColor(int, null)

fun View.placeVisible(visible: Boolean) = if (visible) visibility = View.VISIBLE else visibility = View.INVISIBLE

fun View.show() {
  visibility = View.VISIBLE
}
fun View.hide() {
  visibility = View.INVISIBLE
}
fun View.gone() {
  visibility = View.GONE
}

fun View.animateBottom(
  isVisible: Boolean = true
) {
  val transition: Transition = Slide(Gravity.BOTTOM)
  animatedVisible(isVisible, transition)
}

fun View.animateLeft(
  isVisible: Boolean = true
) {
  val transition: Transition = Slide(Gravity.LEFT)
  animatedVisible(isVisible, transition)
}

fun View.animateRight(
  isVisible: Boolean = true
) {
  val transition: Transition = Slide(Gravity.RIGHT)
  animatedVisible(isVisible, transition)
}

fun View.animateTop(
  isVisible: Boolean = true
) {
  val transition: Transition = Slide(Gravity.TOP)
  animatedVisible(isVisible, transition)
}

fun View.animateFade(
  isVisible: Boolean = true
) {
  val transition: Transition = Fade()
  animatedVisible(isVisible, transition)
}

/**
 * from official document
 */
fun View.animateReveal(
  isVisible: Boolean = true
) {
  post {
    if (isVisible) {
      val cx = width / 2
      val cy = height / 2
      // get the final radius for the clipping circle
      val finalRadius = hypot(cx.toDouble(), cy.toDouble()).toFloat()
      // create the animator for this view (the start radius is zero)
      val anim = ViewAnimationUtils.createCircularReveal(this, cx, cy, 0f, finalRadius)
      // make the view visible and start the animation
      show()
      anim.start()
    } else {
      // get the center for the clipping circle
      val cx = width / 2
      val cy = height / 2
      // get the initial radius for the clipping circle
      val initialRadius = hypot(cx.toDouble(), cy.toDouble()).toFloat()
      // create the animation (the final radius is zero)
      val anim = ViewAnimationUtils.createCircularReveal(this, cx, cy, initialRadius, 0f)
      // make the view invisible when the animation is done
      anim.doOnEnd {
        gone()
      }
      // start the animation
      anim.start()
    }
  }
}

fun View.animatedVisible(
  isVisible: Boolean = true,
  transition: Transition
) {
  transition.duration = 300
  transition.addTarget(this)
  TransitionManager.beginDelayedTransition(
    parent as ViewGroup,
    transition
  )
  if (isVisible) {
    this.show()
  } else {
    this.gone()
  }
}

// dp to pixels
val Int.dp: Int get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()

// float dp to pixels
val Float.dp: Int get() = (this * Resources.getSystem().displayMetrics.density + 0.5f).toInt()
