package com.ricepo.style.view

import android.graphics.Canvas
import android.graphics.Paint
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.text.style.ReplacementSpan
import androidx.annotation.NonNull

//
// Created by <PERSON><PERSON> on 1/9/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * the image height > text height
 */
class CenterAlignTextSpan : ReplacementSpan {

  private var fontSizeSp: Int = -1

  constructor() : super() {
  }

  constructor(fontSizeSp: Int) {
    this.fontSizeSp = fontSizeSp
  }

  override fun getSize(paint: Paint, text: CharSequence, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
    val newPaint = getTextPaint(paint)
    return newPaint.measureText(text, start, end).toInt()
  }

  override fun draw(
    @NonNull canvas: Canvas,
    text: CharSequence,
    start: Int,
    end: Int,
    x: Float,
    top: Int,
    y: Int,
    bottom: Int,
    @NonNull paint: Paint
  ) {
    val newPaint = getTextPaint(paint)
    val fontMetricsInt = newPaint.fontMetricsInt

    canvas.save()

    // stack size overflow
    val staticLayout = StaticLayout(
      text, TextPaint(), canvas.width,
      Layout.Alignment.ALIGN_NORMAL, 1.0f, 0.0f, false
    )

    val offsetY = (y + fontMetricsInt.ascent + y + fontMetricsInt.descent) / 2 - ((top + bottom) / 2)
    // ellipsize end is invalid
//        canvas.drawText(text, start, end, x, y - offsetY.toFloat(), newPaint)

    canvas.translate(x, offsetY.toFloat())
    staticLayout.draw(canvas)
    canvas.restore()
  }

  private fun getTextPaint(srcPaint: Paint): TextPaint {
    val textPaint = TextPaint(srcPaint)
    if (fontSizeSp != -1) {
      textPaint.textSize = (fontSizeSp * textPaint.density)
    }
    return textPaint
  }
}
