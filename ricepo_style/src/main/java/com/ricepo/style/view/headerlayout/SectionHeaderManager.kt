package com.ricepo.style.view.headerlayout

//
// Created by <PERSON><PERSON> on 4/9/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
/**
 * Manages header state.
 *
 *
 */
internal class SectionHeaderManager(
  var headerViewManager: HeaderViewManager,
  private val adapter: SectionAdapter<*>
) {

  private var topSectionHolder: SectionHolder? = null

  private var headerPosition: Int = -1

  /**
   * Checks, whether the header should be updated
   */
  fun checkIsHeaderViewChanged() {
    val topPosition = headerViewManager.firstVisiblePos
    if (!checkIndex(topPosition, adapter.itemCount)) {
      removeHeaderView()
      return
    }

    val viewType = adapter.getItemViewType(topPosition)
    topSectionHolder = adapter.getItemHolder(viewType)

    if (topSectionHolder is SectionHeaderHolder) {
      headerPosition = topPosition
      // because of You must call removeView() on the child's parent first.
      val duplicateHolder = adapter.onCreateHeaderViewHolder(
        viewType,
        headerViewManager.headerViewParent
      )
      adapter.onBindHeaderViewHolder(duplicateHolder, topPosition)
      duplicateHolder?.let {
        headerViewManager.addHeaderView(it.itemView, topPosition + 1)
      }
    } else {
      if (topPosition < headerPosition) {
        removeHeaderView()
      }
    }
  }

  private fun checkIndex(index: Int, itemCount: Int): Boolean {
    return index in 0 until itemCount
  }

  /**
   */
  fun checkFirstVisiblePos() {
    headerViewManager.checkFirstVisiblePos()
  }

  /**
   */
  private fun addHeaderView() {}

  /**
   *
   * @param sectionType Type of the updated section.
   */
  private fun updateHeaderView(sectionType: Short) {}

  /**
   * Notifies [HeaderViewManager] that the header view should be removed.
   */
  private fun removeHeaderView() {
    if (topSectionHolder !is SectionHeaderHolder) {
      headerViewManager.removeHeaderView()
    }
  }
}
