package com.ricepo.style.view.rv.listener

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager

class RecyclerViewLoadMoreScroll : RecyclerView.OnScrollListener {

  private var visibleThreshold = 2
  private lateinit var mOnLoadMoreListener: OnLoadMoreListener
  private var lastVisibleItem: Int = 0
  private var totalItemCount: Int = 0
  private var mLayoutManager: RecyclerView.LayoutManager

  var isLoading: Boolean = false

  fun setOnLoadMoreListener(mOnLoadMoreListener: OnLoadMoreListener) {
    this.mOnLoadMoreListener = mOnLoadMoreListener
  }

  constructor(layoutManager: LinearLayoutManager) {
    this.mLayoutManager = layoutManager
  }

  constructor(layoutManager: GridLayoutManager) {
    this.mLayoutManager = layoutManager
    visibleThreshold *= layoutManager.spanCount
  }

  constructor(layoutManager: StaggeredGridLayoutManager) {
    this.mLayoutManager = layoutManager
    visibleThreshold *= layoutManager.spanCount
  }

  override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
    super.onScrolled(recyclerView, dx, dy)

    if (mLayoutManager.canScrollHorizontally() && dx <= 0) return
    if (mLayoutManager.canScrollVertically() && dy <= 0) return

    totalItemCount = mLayoutManager.itemCount

    if (mLayoutManager is StaggeredGridLayoutManager) {
      val layoutManager = mLayoutManager as StaggeredGridLayoutManager
      val lastVisibleItemPositions =
        layoutManager.findLastVisibleItemPositions(null)
      // get maximum element within the list
      lastVisibleItem = getLastVisibleItem(lastVisibleItemPositions)
    } else if (mLayoutManager is GridLayoutManager) {
      lastVisibleItem = (mLayoutManager as GridLayoutManager).findLastVisibleItemPosition()
    } else if (mLayoutManager is LinearLayoutManager) {
      lastVisibleItem = (mLayoutManager as LinearLayoutManager).findLastVisibleItemPosition()
    }

    if (!isLoading && totalItemCount <= lastVisibleItem + visibleThreshold) {
      isLoading = true
      mOnLoadMoreListener.onLoadMore(lastVisibleItem)
    }
    if (isLoading) {
      // only show progress
      mOnLoadMoreListener.showProgress(lastVisibleItem == (totalItemCount - 1))
    }
  }

  private fun getLastVisibleItem(lastVisibleItemPositions: IntArray): Int {
    var maxSize = 0
    for (i in lastVisibleItemPositions.indices) {
      if (i == 0) {
        maxSize = lastVisibleItemPositions[i]
      } else if (lastVisibleItemPositions[i] > maxSize) {
        maxSize = lastVisibleItemPositions[i]
      }
    }
    return maxSize
  }
}
