package com.ricepo.style.view

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.TextView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.ricepo.style.R

//
// Created by <PERSON><PERSON> on 25/5/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class CaptchaCodeTextView : CoordinatorLayout {

  private lateinit var mView: View
  private lateinit var et_captcha: EditText
  private lateinit var tv_captcha_one: TextView
  private lateinit var tv_captcha_two: TextView
  private lateinit var tv_captcha_three: TextView
  private lateinit var tv_captcha_four: TextView

  private lateinit var mImm: InputMethodManager

  private var codes = mutableListOf<String>()

  private val CAPTCHA_LENGTH = 4

  constructor(context: Context) : super(context) {
    init(context, null)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(context, attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(context, attrs)
    }

  private fun init(context: Context, attrs: AttributeSet?) {
//        mImm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    mImm = context.getSystemService(InputMethodManager::class.java)
    mView = LayoutInflater.from(context).inflate(R.layout.view_captcha_code, this)
    et_captcha = mView.findViewById(R.id.et_captcha)
    tv_captcha_one = mView.findViewById(R.id.tv_captcha_one)
    tv_captcha_two = mView.findViewById(R.id.tv_captcha_two)
    tv_captcha_three = mView.findViewById(R.id.tv_captcha_three)
    tv_captcha_four = mView.findViewById(R.id.tv_captcha_four)
    initEvent()
  }

  private fun initEvent() {
    // core-ktx not reference
//        et_captcha.doAfterTextChanged { text ->
//
//        }

    et_captcha?.addTextChangedListener(object : TextWatcher {
      override fun afterTextChanged(s: Editable?) {
        if (s != null && s.isNotEmpty()) {
          et_captcha.setText("")
          if (codes.size < CAPTCHA_LENGTH) {
            codes.add(s.toString())
            showCode()
          }
        }
      }

      override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
      }

      override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
      }
    })

    // listener of keycode del
    et_captcha?.setOnKeyListener { v, keyCode, event ->
      var result = false
      if (keyCode == KeyEvent.KEYCODE_DEL && event.action == KeyEvent.ACTION_DOWN && codes.size > 0) {
        codes.removeAt(codes.size - 1)
        showCode()
        result = true
      }
      result
    }
  }

  private fun showCode() {
    var code1 = ""
    var code2 = ""
    var code3 = ""
    var code4 = ""

    if (codes.size >= 1) {
      code1 = codes[0]
    }
    if (codes.size >= 2) {
      code2 = codes[1]
    }
    if (codes.size >= 3) {
      code3 = codes[2]
    }
    if (codes.size >= 4) {
      code4 = codes[3]
    }

    tv_captcha_one?.text = code1
    tv_captcha_two?.text = code2
    tv_captcha_three?.text = code3
    tv_captcha_four?.text = code4

    // the callback of input completed
    if (codes != null && codes.size == CAPTCHA_LENGTH) {
      onCompleteListener?.onCompleted(getCaptchaCode())
    }
  }

  fun showSoftInput() {
    if (mImm != null && et_captcha != null) {
      et_captcha.requestFocus()
      et_captcha.postDelayed({ mImm.showSoftInput(et_captcha, 0) }, 100)
    }
  }

  /**
   * clear the captcha code
   */
  fun resetCode() {
    codes.clear()
    showCode()
  }

  /**
   * get the captcha code
   */
  private fun getCaptchaCode(): String? {
    val sb = StringBuilder()
    for (code in codes) {
      sb.append(code)
    }
    return sb.toString()
  }

  var onCompleteListener: OnCompleteListener? = null

//    inline fun onCompleteListener(onCompleteListener: OnCompleteListener) {this.onCompleteListener = onCompleteListener}

//    inline fun onCompleteListener(onCompleteListener: OnCompleteListener) = onCompleteListener

  interface OnCompleteListener {
    fun onCompleted(code: String?)
  }
}
