package com.ricepo.style.view.rv

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.recyclerview.widget.RecyclerView

//
// Created by <PERSON><PERSON> on 15/6/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//

open class NestingRecyclerView @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

  companion object {
    const val TAG = "NestingRecyclerView"
  }

  private var mScrollPointerId = 0
  private var mInitialTouchX = 0
  private var mInitialTouchY = 0
  private var mTouchSlop = 0

  private var lastX = 0f
  private var lastY = 0f
  private var scrolling = false

  init {
    val vc = ViewConfiguration.get(context)
    mTouchSlop = vc.scaledTouchSlop

    addOnScrollListener(object : OnScrollListener() {
      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        scrolling = newState != SCROLL_STATE_IDLE
      }
    })
  }

  override fun onInterceptTouchEvent(e: MotionEvent): Boolean {
    val lm = layoutManager ?: return super.onInterceptTouchEvent(e)
    var allowScroll = true
    when (e.actionMasked) {
      MotionEvent.ACTION_DOWN -> {
        lastX = e.x
        lastY = e.y
        // If we were scrolling, stop now by faking a touch release
        if (scrolling) {
          val newEvent = MotionEvent.obtain(e)
          newEvent.action = MotionEvent.ACTION_UP
          return super.onInterceptTouchEvent(newEvent)
        }
      }
      MotionEvent.ACTION_MOVE -> {
        // We're moving, so check if we're trying
        // to scroll vertically or horizontally so we don't intercept the wrong event.
        val currentX = e.x
        val currentY = e.y
        val dx: Float = Math.abs(currentX - lastX)
        val dy: Float = Math.abs(currentY - lastY)
        allowScroll = if (dy > dx) lm.canScrollVertically() else lm.canScrollHorizontally()
      }
    }
    return if (!allowScroll) {
      false
    } else super.onInterceptTouchEvent(e)
  }

  override fun fling(velocityX: Int, velocityY: Int): Boolean {
    // control the vertical fling speed
//        val velocityY = (velocityY * 0.5).toInt()
    return super.fling(velocityX, velocityY)
  }
}
