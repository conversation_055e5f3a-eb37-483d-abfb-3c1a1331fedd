package com.ricepo.style.view.rv.extend

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.AttributeSet
import android.view.animation.LinearInterpolator
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.style.view.rv.NestingRecyclerView
import java.lang.ref.WeakReference
import kotlin.math.max

/**
 * auto poll and set background recyclerview
 */
class AutoPollRecyclerView : NestingRecyclerView {

  companion object {
    /**
     * the horizontal scroll depth
     */
    var scrollHoriDepth = 0.0
  }

  private var bitmapWidth = 0
  private var bitmapHeight = 0

  private var dstRect: Rect? = null
  private var srcRect: Rect? = null

  private var mScrollY = 0
  private var ratio = 0f

  private val drawHeight = 0f

  private var paint: Paint? = null

  private var mScrollX = 0
  private var drawWidth = 0f

  private var bitmap: Bitmap? = null

  // 45s = 300ms * 150
  private val TIME_AUTO_POLL: Long = 300

  var autoPollTask: AutoPollTask? = null
  private var running = false
  private var canRun = false

  class AutoPollTask(reference: AutoPollRecyclerView) : Runnable {

    private val mReference: WeakReference<AutoPollRecyclerView> = WeakReference(reference)

    override fun run() {
      val recyclerView = mReference.get()
      if (recyclerView != null && recyclerView.running && recyclerView.canRun) {
        // 30s per screen
        val dx = recyclerView.resources.displayMetrics.widthPixels.div(150)

        recyclerView.smoothScrollBy(dx, 0, LinearInterpolator())
        recyclerView.postDelayed(recyclerView.autoPollTask, recyclerView.TIME_AUTO_POLL)
      }
    }
  }

  fun start() {
    if (running) {
      stop()
    }
    canRun = true
    running = true
    postDelayed(autoPollTask, TIME_AUTO_POLL)
  }

  fun stop() {
    running = false
    removeCallbacks(autoPollTask)
  }

  constructor(context: Context) : super(context) {
    init()
  }

  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
    init()
  }

  constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
    init()
  }

  private fun init() {
    autoPollTask = AutoPollTask(this)

    //
    addOnScrollListener(object : OnScrollListener() {

      override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        if (newState == SCROLL_STATE_IDLE) try {
          val scrollX = max(mScrollX, 0)
          scrollHoriDepth = scrollX.toDouble().div(resources.displayMetrics.widthPixels)
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }

      override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        mScrollY += dy
        mScrollX += dx
      }
    })

    srcRect = Rect()
    paint = Paint(Paint.ANTI_ALIAS_FLAG)
  }

  fun setBackgroundBitmap(bitmap: Bitmap?) {
    this.bitmap = bitmap
    bitmapWidth = bitmap?.width ?: 0
    bitmapHeight = bitmap?.height ?: 0
  }

  fun getBackgroundBitmap(): Bitmap? {
    return bitmap
  }

  fun mergeBitmap(leftBitmap: Bitmap?, rightBitmap: Bitmap?, isBaseMax: Boolean): Bitmap? {
    if (leftBitmap == null || leftBitmap.isRecycled ||
      rightBitmap == null || rightBitmap.isRecycled
    ) {
      return null
    }
    var height = 0
    height = if (isBaseMax) {
      if (leftBitmap.height > rightBitmap.height) leftBitmap.height else rightBitmap.height
    } else {
      if (leftBitmap.height < rightBitmap.height) leftBitmap.height else rightBitmap.height
    }
    var tempBitmapL: Bitmap = leftBitmap
    var tempBitmapR: Bitmap = rightBitmap
    if (leftBitmap.height != height) {
      val width = (leftBitmap.width * 1f / leftBitmap.height * height).toInt()
      tempBitmapL = Bitmap.createScaledBitmap(leftBitmap, width, height, false)
    } else if (rightBitmap.height != height) {
      val width = (rightBitmap.width * 1f / rightBitmap.height * height).toInt()
      tempBitmapR = Bitmap.createScaledBitmap(rightBitmap, width, height, false)
    }
    val width = tempBitmapL.width + tempBitmapR.width
    val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(bitmap)
    val leftRect = Rect(0, 0, tempBitmapL.width, tempBitmapL.height)
    val rightRect = Rect(0, 0, tempBitmapR.width, tempBitmapR.height)
    val rightRectT = Rect(tempBitmapL.width, 0, width, height)
    canvas.drawBitmap(tempBitmapL, leftRect, leftRect, null)
    canvas.drawBitmap(tempBitmapR, rightRect, rightRectT, null)
    return bitmap
  }

  override fun onMeasure(widthSpec: Int, heightSpec: Int) {
    super.onMeasure(widthSpec, heightSpec)
    val width = MeasureSpec.getSize(widthSpec)
    val height = MeasureSpec.getSize(heightSpec)
    dstRect = Rect(0, 0, width, height)
    ratio = 1f
    if (bitmapHeight > 0) {
      ratio = height / bitmapHeight.toFloat()
    }
    drawWidth = bitmapWidth * ratio
  }

  override fun onDraw(canvas: Canvas) {

    if (drawWidth < 1) {
      super.onDraw(canvas)
      return
    }
    val srcRect = srcRect ?: return
    val dstRect = dstRect ?: return

    val offsetX = getOffsetX(mScrollX)

    bitmap?.let {
      var left = (offsetX / ratio).toInt()
      var right = ((canvas.width + offsetX) / ratio).toInt()
      if (right > bitmapWidth) {
        right = bitmapWidth
        left = max(0, right - (canvas.width / ratio).toInt())
      }
      srcRect.left = left
      srcRect.top = 0
      srcRect.right = right
      srcRect.bottom = bitmapHeight
      canvas.drawBitmap(it, srcRect, dstRect, paint)
    }
    super.onDraw(canvas)
  }

  private fun getOffsetX(scrollX: Int): Int {
    return if (scrollX < drawWidth) {
      scrollX
    } else if (scrollX < 0) {
      (drawWidth + scrollX).toInt()
    } else {
      getOffsetX((scrollX - drawWidth).toInt())
    }
  }
}
