package com.ricepo.style.view.rv

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.abs

//
// Created by <PERSON><PERSON> on 4/19/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
open class CardManager(context: Context) : RecyclerView.LayoutManager() {
  override fun generateDefaultLayoutParams(): RecyclerView.LayoutParams {
    return RecyclerView.LayoutParams(
      ViewGroup.LayoutParams.WRAP_CONTENT,
      ViewGroup.LayoutParams.WRAP_CONTENT
    )
  }

  override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {
    detachAndScrapAttachedViews(recycler)
    val itemCount = itemCount
    // nothing to do if recycler count is 1
    if (itemCount < 1) {
      return
    }
    // the bottom position
    val bottomPosition = if (itemCount < MAX_COUNT) {
      0
    } else {
      itemCount - MAX_COUNT
    }
    for (i in bottomPosition until itemCount) {
      // get itemView from recycler pool
      val view = recycler.getViewForPosition(i)
      setConstraint(view)
      addView(view)
      // measure itemView
      measureChildWithMargins(view, 0, 0)
      val widthSpace = width - getDecoratedMeasuredWidth(view)
      val heightSpace = height - getDecoratedMeasuredHeight(view)
      // put the view to center
      layoutDecoratedWithMargins(
        view,
        widthSpace / 2,
        heightSpace / 2,
        widthSpace / 2 + getDecoratedMeasuredWidth(view),
        heightSpace / 2 + getDecoratedMeasuredHeight(view)
      )
      // modify the position of view
      val level = abs(i - itemCount + 1)
      // max show interaction need 3 count
      if (level > MAX_COUNT - 3) {
        // scale of view
        view.scaleY = 1 - SCALE_RATIO * (MAX_COUNT - 2)
        // translate of view
        view.translationX = TRANS_RATIO * (MAX_COUNT - 2)
      } else if (level > 0) {
        view.scaleY = 1 - SCALE_RATIO * level
        view.translationX = TRANS_RATIO * level
      }
    }
  }

  open fun setConstraint(view: View) {
  }

  companion object {
    // the max of show amount
    var MAX_COUNT = 3

    // the ratio of scale
    var SCALE_RATIO = 0.03f

    // the ratio of translate
    var TRANS_RATIO = 5f
  }

  init {
    // init the translate ratio
    TRANS_RATIO = (8f * context.resources.displayMetrics.density)
  }
}
