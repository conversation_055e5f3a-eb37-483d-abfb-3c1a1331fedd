package com.ricepo.style.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.text.TextUtils
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import java.lang.ref.WeakReference

//
// Created by <PERSON><PERSON> on 10/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class DrawableAnimationView @JvmOverloads constructor(
  context: Context?,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr), Handler.Callback {

  var mCurAnimPos = 0
  var isRepeat = false
  var mAnimTime = 0

  private var mGravity = 0

  private var mHandler: Handler? = null
  private var mProcessThread: ProcessAnimThread? = null
  private var mCurShowBmp: Bitmap? = null
  private val mAnimDataList: MutableList<AnimData>? = ArrayList()

  private val mTempMatrix = Matrix()

  private var mWidth = 0
  private var mHeight = 0

  private var mHasStarted = false
  private var mHavePause = false

  private var mListener: AnimCallBack? = null

  private fun initParams() {
    mHandler = Handler(this)
    mProcessThread = ProcessAnimThread(context, mHandler)
    mAnimTime = DEFAULT_ANIM_TIME
  }

  fun setGravity(gravity: Int) {
    mGravity = gravity
    invalidate()
  }

  fun clearData() {
    mAnimDataList?.clear()
    // reset pos
    mCurAnimPos = 0
  }

  fun setData(list: List<AnimData>?) {
    if (list != null) {
      mAnimDataList?.addAll(list)
    }
  }

  override fun onDraw(canvas: Canvas) {
    val curShowBmp = mCurShowBmp
    if (curShowBmp != null && !curShowBmp.isRecycled) {
      var x = 0
      var y = 0
      var scaleX = 1f
      var scaleY = 1f
      val bmpWidth = curShowBmp?.width ?: 0
      val bmpHeight = curShowBmp?.height ?: 0
      when (mGravity and Gravity.HORIZONTAL_GRAVITY_MASK) {
        Gravity.LEFT -> x = 0
        Gravity.RIGHT -> x = this.width - bmpWidth
        Gravity.CENTER_HORIZONTAL -> x = (this.width - bmpHeight) / 2
        Gravity.FILL_HORIZONTAL -> {
          if (bmpWidth > 0) {
            scaleX = this.width.toFloat() / bmpWidth.toFloat()
          }
        }
        else -> {
        }
      }
      when (mGravity and Gravity.VERTICAL_GRAVITY_MASK) {
        Gravity.TOP -> y = 0
        Gravity.BOTTOM -> y = this.height - bmpWidth
        Gravity.CENTER_VERTICAL -> y = (this.height - bmpHeight) / 2
        Gravity.FILL_VERTICAL -> {
          if (bmpHeight > 0) {
            scaleY = this.height.toFloat() / bmpHeight.toFloat()
          }
        }
        else -> {
        }
      }
      if (scaleX == 1f && scaleY != 1f) {
        scaleX = scaleY
        when (mGravity and Gravity.HORIZONTAL_GRAVITY_MASK) {
          Gravity.RIGHT -> x = this.width - (bmpWidth * scaleX).toInt()
          Gravity.CENTER_HORIZONTAL -> x = (this.width - (bmpWidth * scaleX).toInt()) / 2
        }
      } else if (scaleX != 1f && scaleY == 1f) {
        scaleY = scaleX
        when (mGravity and Gravity.VERTICAL_GRAVITY_MASK) {
          Gravity.BOTTOM -> y = this.height - (bmpHeight * scaleY).toInt()
          Gravity.CENTER_VERTICAL -> y = (this.height - (bmpHeight * scaleY).toInt()) / 2
        }
      }
      mTempMatrix.reset()
      mTempMatrix.postScale(scaleX, scaleY)
      mTempMatrix.postTranslate(x.toFloat(), y.toFloat())
      canvas.drawBitmap(curShowBmp, mTempMatrix, null)
    }
  }

  fun start() {
    mHasStarted = true
    if (mWidth == 0 || mHeight == 0) {
      return
    }
    startPlay()
  }

  private fun startPlay() {
    if (mAnimDataList != null && mAnimDataList.size > 0) {
      mCurAnimPos = 0
      val animData = mAnimDataList[mCurAnimPos]
      mCurShowBmp = getBitmap(context, animData.filePath, mWidth, mHeight)
      invalidate()
      if (mListener != null) {
        mListener?.onAnimChange(mCurAnimPos, mCurShowBmp)
      }
      checkIsPlayNext()
    }
  }

  private fun playNext(curAnimPosition: Int) {
    val msg = Message.obtain()
    msg.what = PROCESS_DELAY
    msg.arg1 = curAnimPosition
    mHandler?.sendMessageDelayed(msg, mAnimTime.toLong())
  }

  override fun onDetachedFromWindow() {
    super.onDetachedFromWindow()
    quit()
  }

  override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
    super.onSizeChanged(w, h, oldw, oldh)
    mWidth = w
    mHeight = h
    if (mProcessThread != null) {
      mProcessThread?.setSize(w, h)
    }
    if (mHasStarted) {
      startPlay()
    }
  }

  fun resume() {
    if (mHavePause && mHasStarted) {
      checkIsPlayNext()
    }
  }

  fun pause() {
    mHavePause = true
    mHandler?.removeMessages(PROCESS_DELAY)
  }

  fun quit() {
    mHasStarted = false
    if (mProcessThread != null) {
      mProcessThread?.clearAll()
    }
    if (mHandler != null) {
      mHandler?.removeCallbacksAndMessages(null)
      mHandler = null
    }
  }

  override fun handleMessage(msg: Message): Boolean {
    when (msg.what) {
      PROCESS_ANIM_FINISH -> {
        val bitmap = msg.obj as Bitmap
        if (bitmap != null) {
          if (mCurShowBmp != null) {
            mCurShowBmp?.recycle()
            mCurShowBmp = null
          }
          mCurShowBmp = bitmap
          if (mListener != null) {
            mListener?.onAnimChange(mCurAnimPos, bitmap)
          }
          invalidate()
        }
        checkIsPlayNext()
      }
      PROCESS_DELAY -> {
        val curAnimPosition = msg.arg1
        val data = mAnimDataList?.get(curAnimPosition)
        mProcessThread?.processData(data)
      }
    }
    return true
  }

  private fun checkIsPlayNext() {
    mCurAnimPos++
    val size = mAnimDataList?.size ?: 0
    if (mCurAnimPos >= size) {
      if (isRepeat) {
        mCurAnimPos = 0
        playNext(mCurAnimPos)
      } else {
        if (mListener != null && mCurAnimPos == size) {
          mListener?.onAnimEnd()
        }
      }
    } else {
      playNext(mCurAnimPos)
    }
  }

  fun setAnimCallBack(callBack: AnimCallBack?) {
    mListener = callBack
  }

  interface AnimCallBack {
    fun onAnimChange(position: Int, bitmap: Bitmap?)
    fun onAnimEnd()
  }

  class AnimData {
    var filePath: Any? = null
  }

  class ProcessAnimThread(context: Context, private val mUiHandler: Handler?) {
    private var mHandlerThread: HandlerThread? = null
    private var mProcessHandler: Handler? = null
    private val mCurAnimData: AnimData? = null
    private var mWidth = 0
    private var mHeight = 0
    private val mContext: WeakReference<Context> = WeakReference(context)
    fun setSize(width: Int, height: Int) {
      mWidth = width
      mHeight = height
    }

    private fun init() {
      mHandlerThread = HandlerThread("process_anim_thread")
      mHandlerThread?.start()
      mProcessHandler = mHandlerThread?.looper?.let {
        Handler(
          it
        ) { msg ->
          when (msg.what) {
            PROCESS_DATA -> {
              val animData = msg.obj as AnimData
              val bitmap = getBitmap(
                mContext.get(),
                animData.filePath,
                mWidth,
                mHeight
              )
              if (bitmap != null) {
                val finishMsg = Message.obtain()
                finishMsg.what = PROCESS_ANIM_FINISH
                finishMsg.obj = bitmap
                mUiHandler?.sendMessage(finishMsg)
              }
            }
          }
          true
        }
      }
    }

    fun processData(animData: AnimData?) {
      if (mHandlerThread == null) {
        init()
      }
      if (animData != null) {
        val msg = Message.obtain()
        msg.what = PROCESS_DATA
        msg.obj = animData
        try {
          mProcessHandler?.sendMessage(msg)
        } catch (e: Exception) {
          e.printStackTrace()
        }
      }
    }

    fun clearAll() {
      mHandlerThread?.quit()
      mHandlerThread = null
    }

    init {
      init()
    }
  }

  companion object {
    const val DEFAULT_ANIM_TIME = 100
    const val PROCESS_DATA = 1
    const val PROCESS_ANIM_FINISH = 1 shl 1
    const val PROCESS_DELAY = 1 shl 2

    fun getBitmap(context: Context?, path: Any?, width: Int, height: Int): Bitmap? {
      var bitmap: Bitmap? = null
      if (path is String) {
        bitmap = createBitmap(path as String?, width, height)
      } else if (path is Int) {
        if (context != null) {
          bitmap = createBitmap(context, path, width, height)
        }
      }
      return bitmap
    }

    private fun createBitmap(picPath: String?, width: Int, height: Int): Bitmap? {
      if (!TextUtils.isEmpty(picPath)) {
        val opts = BitmapFactory.Options()
        opts.inJustDecodeBounds = true
        BitmapFactory.decodeFile(picPath, opts)
        opts.inSampleSize = caculateInSampleSize(opts, width, height)
        opts.inJustDecodeBounds = false
        return BitmapFactory.decodeFile(picPath, opts)
      }
      return null
    }

    private fun createBitmap(context: Context, resId: Int, width: Int, height: Int): Bitmap? {
      if (resId != 0) {
        val opts = BitmapFactory.Options()
        opts.inJustDecodeBounds = true
        BitmapFactory.decodeResource(context.resources, resId, opts)
        opts.inSampleSize = caculateInSampleSize(opts, width, height)
        opts.inJustDecodeBounds = false
        return BitmapFactory.decodeResource(context.resources, resId, opts)
      }
      return null
    }

    private fun caculateInSampleSize(
      options: BitmapFactory.Options,
      reqWidth: Int,
      reqHeight: Int
    ): Int {
      val width = options.outWidth
      val height = options.outHeight
      var inSampleSize = 1
      if (width > reqWidth || height > reqHeight) {
        val widthRadio = Math.round(width * 1.0f / reqWidth)
        val heightRadio = Math.round(height * 1.0f / reqHeight)
        inSampleSize = Math.max(widthRadio, heightRadio)
      }
      return inSampleSize
    }
  }

  init {
    initParams()
  }
}
