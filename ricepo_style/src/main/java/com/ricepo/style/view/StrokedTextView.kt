package com.ricepo.style.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.util.TypedValue
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.ricepo.style.DisplayUtil.sp2Px
import com.ricepo.style.R

class StrokedTextView : AppCompatTextView {

  private var attrs: AttributeSet? = null

  // fields
  private var _strokeColor = 0
  private var _strokeWidth = 0f
  private var isDrawing = false

  // outline stroke text
  private var borderText: TextView? = null

  constructor(context: Context) : super(context) {
    borderText = TextView(context)
    init(context, null)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    borderText = TextView(context, attrs)
    init(context, attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      borderText = TextView(context, attrs, defStyleAttr)
      init(context, attrs)
    }

  private fun init(context: Context, attrs: AttributeSet?) {
    if (attrs != null) {
      val a = context.obtainStyledAttributes(attrs, R.styleable.StrokedTextAttrs)
      _strokeColor = a.getColor(
        R.styleable.StrokedTextAttrs_textStrokeColor,
        currentTextColor
      )
      _strokeWidth = a.getFloat(
        R.styleable.StrokedTextAttrs_textStrokeWidth,
        DEFAULT_STROKE_WIDTH.toFloat()
      )
      val _shadowColor = a.getColor(R.styleable.StrokedTextAttrs_textShadowColor, 0)
      val _shadowRadius = a.getDimension(R.styleable.StrokedTextAttrs_textShadowRadius, 0f)
      val _shadowDy = a.getDimension(R.styleable.StrokedTextAttrs_textShadowDy, 0f)
      borderText?.setShadowLayer(_shadowRadius, 0f, _shadowDy, _shadowColor)
      a.recycle()
    } else {
      _strokeColor = currentTextColor
      _strokeWidth = DEFAULT_STROKE_WIDTH.toFloat()
    }
    setStrokeColor(_strokeColor)
    setStrokeWidth(_strokeWidth)
    borderText?.paint?.let {
      it.style = Paint.Style.STROKE
    }
  }

  override fun invalidate() {
    // Ignore invalidate() calls when isDrawing == true
    // (setTextColor(color) calls will trigger them,
    // creating an infinite loop)
    if (isDrawing) return
    super.invalidate()
  }

  fun setStrokeColor(color: Int) {
    _strokeColor = color
    borderText?.let {
      it.setTextColor(_strokeColor)
      it.gravity = gravity
    }
  }

  fun setStrokeWidth(width: Float) {
    // convert values specified in dp in XML layout to
    // px, otherwise stroke width would appear different
    // on different screens
    _strokeWidth = sp2Px(context, width).toFloat()
    borderText?.paint?.let {
      it.strokeWidth = _strokeWidth
    }
  }

  fun setStrokeWidth(unit: Int, width: Float) {
    _strokeWidth = TypedValue.applyDimension(
      unit, width, context.resources.displayMetrics
    )
  }

  override fun setLayoutParams(params: ViewGroup.LayoutParams?) {
    super.setLayoutParams(params)
    borderText?.layoutParams = params
  }

  override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
    val text = borderText?.text
    if (text == null || !text.equals(getText())) {
      borderText?.text = getText()
      this.postInvalidate()
    }

    super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    borderText?.measure(widthMeasureSpec, heightMeasureSpec)
  }

  override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
    super.onLayout(changed, left, top, right, bottom)
    borderText?.layout(left, top, right, bottom)
  }

  // overridden methods
  override fun onDraw(canvas: Canvas) {
//        if (_strokeWidth > 0) {
//            isDrawing = true
//            //set paint to fill mode
//            val p: Paint = paint
//            p.style = Paint.Style.FILL
//            //draw the fill part of text
//            super.onDraw(canvas)
//            //save the text color
//            val currentTextColor = currentTextColor
//            //set paint to stroke mode and specify
//            //stroke color and width
//            p.style = Paint.Style.STROKE
//            p.strokeWidth = _strokeWidth
//            setTextColor(_strokeColor)
//            //draw text stroke
//            super.onDraw(canvas)
//            //revert the color back to the one
//            //initially specified
//            setTextColor(currentTextColor)
//            isDrawing = false
//        } else {
//            super.onDraw(canvas)
//        }
    borderText?.draw(canvas)
    super.onDraw(canvas)
  }

  companion object {
    private const val DEFAULT_STROKE_WIDTH = 0
  }
}
