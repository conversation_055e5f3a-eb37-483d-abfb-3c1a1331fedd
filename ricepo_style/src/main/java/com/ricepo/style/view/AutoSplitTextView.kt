package com.ricepo.style.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.StaticLayout
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import kotlin.math.ceil

//
// Created by <PERSON><PERSON> on 13/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class AutoSplitTextView : AppCompatTextView {

  constructor(context: Context) : super(context) {
    init(context, null)
  }

  constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
    init(context, attrs)
  }

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
    super(context, attrs, defStyleAttr) {
      init(context, attrs)
    }

  private fun init(context: Context, attrs: AttributeSet?) {
  }

  private var mLineY: Float = 0f

  private var mViewWidth: Int = 0

  override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
//        if (MeasureSpec.getMode(widthMeasureSpec) == MeasureSpec.EXACTLY
//            && MeasureSpec.getMode(heightMeasureSpec) == MeasureSpec.EXACTLY
//            && width > 0
//            && height > 0) {
//            val newText = autoSplitText()
//            if (!TextUtils.isEmpty(newText)) {
//                text = newText
//            }
//        }

    super.onMeasure(widthMeasureSpec, heightMeasureSpec)
  }

  override fun onDraw(canvas: Canvas) {
    super.onDraw(canvas)
  }

  private fun drawText(canvas: Canvas) {
    val paint = paint
    paint.color = currentTextColor
    paint.drawableState = drawableState
    mViewWidth = measuredWidth
    val text = text.toString()
    mLineY = 0f
    mLineY += textSize
    val layout = layout ?: return

    val fm: Paint.FontMetrics = paint.fontMetrics
    var textHeight = ceil(fm.descent - fm.ascent)
    textHeight = (textHeight * layout.spacingMultiplier + layout.spacingAdd)

    for (i in 0 until layout.lineCount) {
      val lineStart = layout.getLineStart(i)
      val lineEnd = layout.getLineEnd(i)
      val width = StaticLayout.getDesiredWidth(
        text, lineStart,
        lineEnd, getPaint()
      )
      var line = text.substring(lineStart, lineEnd)
      // trim blank space
      line = line.trim()
      // replace ¥
      line = line.replace("￥", "¥")
      canvas.drawText(line, 0f, mLineY, paint)
//            if (i < layout.lineCount - 1) {
//                if (needScale(line)) {
//                    drawScaledText(Canvas, lineStart, line, width)
//                } else {
//                    Canvas.drawText(line, 0f, mLineY, paint)
//                }
//            } else {
//                Canvas.drawText(line, 0f, mLineY, paint)
//            }
      mLineY += textHeight
    }
  }

  /**
   * scale text to fill entire line
   */
  private fun drawScaledText(
    Canvas: Canvas,
    lineStart: Int,
    line: String,
    lineWidth: Float
  ) {
    var line = line
    var x = 0f
    if (isFirstLineOfParagraph(lineStart, line)) {
      val blanks = "  "
      Canvas.drawText(blanks, x, mLineY, paint)
      val bw = StaticLayout.getDesiredWidth(blanks, paint)
      x += bw
      line = line.substring(3)
    }
    val gapCount = line.length - 1
    var i = 0
    if (line.length > 2 && line[0].toInt() == 12288 && line[1].toInt() == 12288) {
      val substring = line.substring(0, 2)
      val cw = StaticLayout.getDesiredWidth(substring, paint)
      Canvas.drawText(substring, x, mLineY, paint)
      x += cw
      i += 2
    }
    val d: Float = (mViewWidth - lineWidth) / gapCount
    while (i < line.length) {
      val c = line[i].toString()
      val cw = StaticLayout.getDesiredWidth(c, paint)
      Canvas.drawText(c, x, mLineY, paint)
      x += cw + d
      i++
    }
  }

  private fun isFirstLineOfParagraph(lineStart: Int, line: String): Boolean {
    return line.length > 3 && line[0] == ' ' && line[1] == ' '
  }

  /**
   * need scale text to show
   */
  private fun needScale(line: String?): Boolean {
    return if (line == null || line.isEmpty()) {
      false
    } else {
      line[line.length - 1] != '\n'
    }
  }

  /**
   * auto split the text of TextView
   */
  fun autoSplitText(): CharSequence {
    val charSequence = this.text
    val rawText = charSequence.toString()
    val tvPaint = this.paint
    this.text = rawText
    val tvWidth = this.width - this.paddingLeft - this.paddingRight
    val rawTextLines = rawText.replace("\r", "").split("\n")

    val sbNewText = StringBuilder()
    rawTextLines.forEach { rawTextLine ->
      if (tvPaint.measureText(rawTextLine) <= tvWidth) {
        sbNewText.append(rawTextLine.trim())
      } else {
        var lineWidth = 0f
        for (cnt in rawTextLine.indices) {
          val ch = rawTextLine.get(cnt)
          lineWidth += tvPaint.measureText(ch.toString())
          if (lineWidth <= tvWidth) {
            sbNewText.append(ch)
          } else {
            sbNewText.append("\n")
            lineWidth = 0f
          }
        }
      }
      sbNewText.append("\n")
    }

    if (!rawText.endsWith("\n")) {
      sbNewText.deleteCharAt(sbNewText.length - 1)
    }

    return if (charSequence is Spanned) {
      val spannableStringBuilder = SpannableStringBuilder(charSequence)
      if (sbNewText.toString().contains("\n")) {
        val split = sbNewText.toString().split("\n".toRegex()).toTypedArray()
        var tempIndex = 0
        for (i in split.indices) {
          if (i != split.size - 1) {
            val s = split[i]
            tempIndex += s.length + i
            try {
              spannableStringBuilder.insert(tempIndex, "\n")
            } catch (e: Exception) {
              e.printStackTrace()
            }
          }
        }
      }
      spannableStringBuilder
    } else {
      sbNewText
    }
  }
}
