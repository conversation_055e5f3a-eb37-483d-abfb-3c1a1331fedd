/*
 */
package com.ricepo.style.view.headerlayout

import android.view.View
import android.view.ViewGroup

/**
 * Interface for interaction with header view in [SectionHeaderLayout].
 */
internal interface HeaderViewManager {
  val firstVisiblePos: Int
  fun checkFirstVisiblePos()
  fun addHeaderView(headerView: View, nextHeaderPos: Int)
  fun removeHeaderView()
  fun translateHeaderView(nextHeaderPos: Int)
  val headerViewParent: ViewGroup
}
