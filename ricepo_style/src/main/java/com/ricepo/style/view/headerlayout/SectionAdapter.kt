package com.ricepo.style.view.headerlayout

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

//
// Created by <PERSON><PERSON> on 4/12/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
abstract class SectionAdapter<T> : RecyclerView.Adapter<SectionHolder>() {

  /**
   * the viewType to holder
   */
  var holders: MutableMap<Int, SectionHolder> = mutableMapOf()

  fun getItemHolder(viewType: Int): SectionHolder? {
    return if (holders.contains(viewType)) {
      holders[viewType]
    } else null
  }

  fun onCreateHeaderViewHolder(viewType: Int, parent: ViewGroup): SectionHolder? {
    return holders[viewType]?.let {
      it.createHeaderViewHolder(this, parent)
    }
  }

  fun onBindHeaderViewHolder(holder: SectionHolder?, position: Int) {
    if (holder is SectionHeaderHolder) {
      holder.bindHeader(this, position)
    }
  }
}

open class SectionHolder(view: View) : RecyclerView.ViewHolder(view) {
  open fun createHeaderViewHolder(adapter: SectionAdapter<*>, parent: ViewGroup): SectionHolder? {
    return null
  }
}

open class SectionItemHolder(view: View) : SectionHolder(view)

open class SectionHeaderHolder(view: View) : SectionHolder(view) {
  open fun bindHeader(adapter: SectionAdapter<*>, position: Int) {
  }
}
