package com.ricepo.style.view.rv

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class HorizontalRecyclerViewScrollListener(private val listener: OnItemCoverListener) :
  RecyclerView.OnScrollListener() {

  private var itemBounds: IntArray? = null

  override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
    super.onScrolled(recyclerView, dx, dy)
//        val adapter = recyclerView.adapter ?: return
//        if (itemBounds == null) fillItemBounds(adapter.itemCount, recyclerView)
//        val itemBounds = itemBounds ?: return
//        for (i in itemBounds.indices) {
//            if (isInChildItemsRange(
//                    recyclerView.computeHorizontalScrollOffset(),
//                    itemBounds[i],
//                    OFFSET_RANGE
//                )
//            ) listener.onItemCover(i)
//        }
  }

  override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
    super.onScrollStateChanged(recyclerView, newState)
    val layoutManager = recyclerView.layoutManager
    if (layoutManager is LinearLayoutManager &&
      recyclerView.scrollState == RecyclerView.SCROLL_STATE_IDLE
    ) {
      val position = layoutManager.findFirstCompletelyVisibleItemPosition()
      listener.onItemCover(position)
    }
  }

  private fun fillItemBounds(placesCount: Int, recyclerView: RecyclerView) {
    itemBounds = IntArray(placesCount)
    val childWidth =
      (recyclerView.computeHorizontalScrollRange() - recyclerView.computeHorizontalScrollExtent()) / placesCount
    for (i in 0 until placesCount) {
      itemBounds!![i] = ((childWidth * i + childWidth * (i + 1)) / 2 * COVER_FACTOR).toInt()
    }
  }

  private fun isInChildItemsRange(offset: Int, itemBound: Int, range: Int): Boolean {
    val rangeMin = itemBound - range
    val rangeMax = itemBound + range
    return Math.min(rangeMin, rangeMax) <= offset && Math.max(rangeMin, rangeMax) >= offset
  }

  interface OnItemCoverListener {
    fun onItemCover(position: Int)
  }

  companion object {
    private const val OFFSET_RANGE = 50
    private const val COVER_FACTOR = 0.7
  }
}
