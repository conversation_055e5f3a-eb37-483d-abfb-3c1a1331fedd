/*
 */
package com.ricepo.style.view.headerlayout

import android.annotation.TargetApi
import android.content.Context
import android.os.Build
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.RelativeLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ricepo.style.DisplayUtil

/**
 * Holder layout for the header view of the RecyclerView with sections.
 *
 *
 */
class SectionHeaderLayout : RelativeLayout {

  private var recyclerView: RecyclerView? = null
  private var headerManager: SectionHeaderManager? = null

  constructor(context: Context?) : super(context) {}
  constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {}
  constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
    context,
    attrs,
    defStyleAttr
  ) {
  }

  @TargetApi(Build.VERSION_CODES.LOLLIPOP)
  constructor(
    context: Context?,
    attrs: AttributeSet?,
    defStyleAttr: Int,
    defStyleRes: Int
  ) : super(context, attrs, defStyleAttr, defStyleRes) {
  }

  /**
   * Attaches to the given RecyclerView and SectionDataManager. Adds [.onScrollListener] to
   * the given RecyclerView to manage header view while scrolling. RecyclerView's layout manager
   * should be a successor of LinearLayoutManager.
   *
   * @param recyclerView       RecyclerView to attach to.
   */
  fun attachTo(recyclerView: RecyclerView) {
    this.recyclerView = recyclerView
    val adapter = recyclerView.adapter
    if (adapter is SectionAdapter<*>) else return
    headerManager = SectionHeaderManager(headerViewManager, adapter)
    recyclerView.addOnScrollListener(onScrollListener)
    headerManager?.checkIsHeaderViewChanged()
  }

  /**
   * Returns whether this SectionHeaderLayout has been attached to RecyclerView and
   * SectionDataManager.
   *
   * @return True if it has been attached, false otherwise.
   */
  val isAttached: Boolean
    get() = headerManager != null

  /**
   * Detaches from RecyclerView
   */
  fun detach() {
    if (!isAttached) {
      throw RuntimeException(
        "SectionHeaderLayout hasn't been attached " +
          "to any RecyclerView and SectionDataManager."
      )
    }
    recyclerView?.removeOnScrollListener(onScrollListener)
    headerViewManager.removeHeaderView()
    recyclerView = null
    headerManager = null
  }

  /**
   * to interact with the header view.
   */
  private val headerViewManager: HeaderViewManager = object : HeaderViewManager {
    override val firstVisiblePos: Int
      get() = (recyclerView?.layoutManager as LinearLayoutManager?)
        ?.findFirstVisibleItemPosition() ?: 0

    override fun checkFirstVisiblePos() {
      checkHeaderView()
    }

    override fun addHeaderView(headerView: View, nextHeaderPos: Int) {
      val newParams = LayoutParams(headerView.layoutParams)
      newParams.addRule(ALIGN_TOP)
      headerView.layoutParams = newParams
      runJustBeforeBeingDrawn(headerView) {
        headerView.translationY = calcTranslation(headerView.height, nextHeaderPos).toFloat()
        headerView.requestLayout()
      }
      resetLayout()
      addView(headerView)
      removePrevHeaderView()
    }

    /**
     * Uses postponed runnable, because removeViewAt(int)
     * should not be invoked from drawing related methods.
     */
    override fun removeHeaderView() {
      if (childCount > 1) {
        post {
          if (childCount > 1) {
            removeViewAt(1)
          }
        }
      }
    }

    override fun translateHeaderView(nextHeaderPos: Int) {
      if (childCount > 1) {
        val headerView = getChildAt(childCount - 1)
        runJustBeforeBeingDrawn(headerView) {
          headerView.translationY =
            calcTranslation(headerView.height, nextHeaderPos).toFloat()
          headerView.requestLayout()
        }
      }
    }

    override val headerViewParent: ViewGroup
      get() = this@SectionHeaderLayout
  }

  /**
   * close the distance between the layout and the title bar
   */
  private fun resetLayout() {
    val params = this.layoutParams
    if (params is CoordinatorLayout.LayoutParams) {
      params.topMargin = - (DisplayUtil.dp2PxOffset(20f))
      this.layoutParams = params
    }
  }

  /**
   * Notifies [com.ricepo.style.view.headerlayout.SectionDataManager.HeaderManager] that
   * the RecyclerView was scrolled, so the header view could have been changed.
   */
  private val onScrollListener: RecyclerView.OnScrollListener =
    object : RecyclerView.OnScrollListener() {
      override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)
        headerManager?.checkIsHeaderViewChanged()
      }
    }

  /**
   * Notifies [com.ricepo.style.view.headerlayout.SectionDataManager.HeaderManager] that
   * the header view could have been changed. Uses [.runJustBeforeBeingDrawn] to provide a correct first
   * visible item position after the update.
   */
  private fun checkHeaderView() {
    runJustBeforeBeingDrawn(recyclerView) { headerManager?.checkIsHeaderViewChanged() }
  }

  /**
   * Removes previous header view if exists. Uses postponed runnable, because removeViewAt(int)
   * should not be invoked from drawing related methods (e.g. [.onScrollListener] is invoked
   * from RecyclerView's onLayout(boolean, int, int, int, int)).
   */
  private fun removePrevHeaderView() {
    if (childCount > 2) {
      post {
        if (childCount > 2) {
          removeViewAt(1)
        }
      }
    }
  }

  /**
   * Calculates yTranslation for the current header view based on its height and next header
   * position.
   *
   * @param headerHeight  Height of the current header view in px.
   * @param nextHeaderPos Adapter position of the next header view.
   * @return Calculated yTranslation for the header view.
   */
  private fun calcTranslation(headerHeight: Int, nextHeaderPos: Int): Int {
    val nextHeaderView = recyclerView?.layoutManager?.findViewByPosition(nextHeaderPos)
    if (nextHeaderView != null) {
//            val topOffset = nextHeaderView.top + nextHeaderView.paddingTop
//            val offset = headerHeight - topOffset
//            if (offset > 0) return -offset

      return -(headerHeight * 0.1).toInt()
    }
    return 0
  }

  companion object {
    /**
     * Runs the code just before the given view is being drawn so that its size has been already
     * calculated.
     *
     * @param view     View to be drawn.
     * @param runnable Code to run.
     */
    private fun runJustBeforeBeingDrawn(view: View?, runnable: Runnable) {
      val onPreDrawListener: ViewTreeObserver.OnPreDrawListener =
        object : ViewTreeObserver.OnPreDrawListener {
          override fun onPreDraw(): Boolean {
            view?.viewTreeObserver?.removeOnPreDrawListener(this)
            runnable.run()
            return true
          }
        }
      view?.viewTreeObserver?.addOnPreDrawListener(onPreDrawListener)
    }
  }
}
