package com.ricepo.style.view.rv

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

//
// Created by <PERSON><PERSON> on 31/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class LinearHeightLayoutManager : LinearLayoutManager {

  constructor(context: Context) : super(context) {}

  constructor(context: Context, orientation: Int, reverseLayout: Boolean) :
    super(context, orientation, reverseLayout) {}

  constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int, defStyleRes: Int) :
    super(context, attrs, defStyleAttr, defStyleRes) {}

  override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {
    super.onLayoutChildren(recycler, state)
    detachAndScrapAttachedViews(recycler)
    calculateChildrenSite(recycler)
  }

  var totalHeight = 0

  private fun calculateChildrenSite(recycler: RecyclerView.Recycler) {
    totalHeight = 0
    for (i in 0 until itemCount) {
      val view: View = recycler.getViewForPosition(i)
      addView(view)
      measureChildWithMargins(view, 0, 0)
      val width = getDecoratedMeasuredWidth(view)
      val height = getDecoratedMeasuredHeight(view)
      val mTmpRect = Rect()
      calculateItemDecorationsForChild(view, mTmpRect)

      layoutDecorated(view, 0, totalHeight, width, totalHeight + height)
      totalHeight += height
    }
  }
}
