package com.ricepo.style.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Paint.Align
import android.graphics.Typeface
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.ricepo.style.R

//
// Created by <PERSON><PERSON> on 29/10/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class VerticalTextView(context: Context?, attrs: AttributeSet?, defStyle: Int) :
  View(context, attrs, defStyle) {
  private val CHINESE_ENGLISH_CHAR_PADDING = 4f
  private var ENGLISH_CHAR_PADDING = 0
  private var mNormalCharPadding = 0
  private var mAlwaysTransChars: String? = null
  private var mText: String? = ""
  private var mCustomMaxHeight = 0
  private var mFontBaselinePadding = 0f
  private var mFontHeight = 0
  private var mHeight = 0
  private var mLastCanShowCharIndex = 0
  private var mLeftLinePaint: Paint? = null
  private var mLineSpace = 0
  private var mLineWidth = 0
  private var mMaxHeight = 0
  private var mMaxLines = 0
  private var mNeedCollipseEnd = false
  private var mNeedLeftLine = false
  private var mNeedSepLine = false
  private var mNormalCharPaddingTop = 0
  private var mOnRealLineChangeListener: OnRealLineChangeListener? = null
  private var mPaint: Paint? = null
  private var mSepPaint: Paint? = null
  private var mTextSize = 0
  private var mTextColor = 0
  private var mTransAfterEngChars: String? = null
  private var mWidth = 0

  interface OnRealLineChangeListener {
    fun realLineChange(i: Int)
  }

  @JvmOverloads
  constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0) {
    val a = context.obtainStyledAttributes(
      attrs,
      R.styleable.VerticalTextView
    )
    mTextColor = a.getColor(R.styleable.VerticalTextView_textColor, DEFAULT_COLOR)
    mTextSize = a.getDimensionPixelSize(
      R.styleable.VerticalTextView_textSize,
      DEFAULT_TEXT_SIZE
    )
    mNeedSepLine = a.getBoolean(R.styleable.VerticalTextView_needSepLine, false)
    mText = a.getString(R.styleable.VerticalTextView_text)
    mNeedCollipseEnd = a.getBoolean(R.styleable.VerticalTextView_needCollipseEnd, false)
    mCustomMaxHeight = a.getDimensionPixelSize(
      R.styleable.VerticalTextView_customMaxHeight,
      DEFAULT_CUSTOM_MAX_HEIGHT
    )
    mMaxLines = a.getInt(R.styleable.VerticalTextView_maxLines, DEFAULT_MAX_LINES)
    mNormalCharPaddingTop = a.getDimensionPixelSize(
      R.styleable.VerticalTextView_normalCharPaddingTop,
      DEFAULT_NORMAL_CHAR_PADDINGTOP
    )
    mNeedLeftLine = a.getBoolean(R.styleable.VerticalTextView_needLeftLine, false)
    mNormalCharPadding = a.getDimensionPixelSize(
      R.styleable.VerticalTextView_normalCharPadding,
      DEFAULT_NORMAL_CHAR_PADDING
    )
    mLineSpace =
      a.getDimensionPixelSize(R.styleable.VerticalTextView_lineSpace, DEFAULT_LINE_SPACE)
    a.recycle()
    setTextColor(mTextColor)
    setTextSize(mTextSize)
    setNeedLeftLine(mNeedLeftLine)
  }

  fun setOnRealLineChangeListener(onRealLineChangeListener: OnRealLineChangeListener?) {
    mOnRealLineChangeListener = onRealLineChangeListener
  }

  fun setNeedCollipseEnd(need: Boolean) {
    mNeedCollipseEnd = need
  }

  fun setText(contentText: String?) {
    mText = contentText
    requestLayout()
    invalidate()
  }

  fun setCustomMaxHeight(maxHeight: Int) {
    mCustomMaxHeight = dip2px(context, maxHeight.toFloat())
  }

  fun setMaxLines(maxLines: Int) {
    mMaxLines = maxLines
  }

  fun setNormalCharPaddingTop(normalCharPaddingTop: Int) {
    mNormalCharPaddingTop = dip2px(context, normalCharPaddingTop.toFloat())
  }

  fun setNeedLeftLine(needLeftLine: Boolean) {
    mNeedLeftLine = needLeftLine
    if (mNeedLeftLine) {
      mLeftLinePaint = Paint(1)
      mLeftLinePaint?.strokeWidth = dip2px(context, 2.0f).toFloat()
      mLeftLinePaint?.style = Paint.Style.STROKE
      mLeftLinePaint?.color = Color.BLACK
    }
  }

  fun setTextSize(textSize: Int) {
    mTextSize = textSize
    mPaint?.textSize = textSize.toFloat()
  }

  fun setTextColor(textColor: Int) {
    mPaint?.color = textColor
    mSepPaint?.color = textColor
  }

  fun setTypeface(typeface: Typeface?) {
    mPaint?.typeface = typeface
  }

  fun setCharPadding(padding: Int) {
    mNormalCharPadding = padding
  }

  fun setLineSpace(lineSpace: Int) {
    mLineSpace = dip2px(context, lineSpace.toFloat())
  }

  private fun init() {
    mPaint = Paint()
    val paint = mPaint ?: return
    paint.textAlign = Align.CENTER
    paint.isAntiAlias = true
    paint.color = Color.BLUE
    paint.textSize = 24.0f
    ENGLISH_CHAR_PADDING = 0
    mSepPaint = Paint(1)
    mSepPaint?.color = Color.BLUE
    mSepPaint?.strokeWidth = 3.0f
    mSepPaint?.style = Paint.Style.STROKE
    val china = charArrayOf(
      ',',
      '.',
      '!',
      '\"',
      '\"',
      '[',
      ']',
      '(',
      ')',
      ':',
      '\'',
      '\\',
      '/',
      '·',
      '，',
      '。',
      '！',
      '“',
      '”',
      '［',
      '］',
      '（',
      '）',
      '：',
      '、',
      '／'
    )
    mAlwaysTransChars = String(
      charArrayOf(
        '\"',
        '\"',
        '[',
        ']',
        '(',
        ')',
        '\'',
        '“',
        '”',
        '［',
        '］',
        '（',
        '）',
        '《',
        '》'
      )
    )
    mTransAfterEngChars = String(china)
  }

  override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
    mHeight = layoutParams.height
//        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
//        if (mCustomMaxHeight == 0 || mCustomMaxHeight > heightSize) {
//            mCustomMaxHeight = heightSize
//        }
    mWidth = measureWidth()
    setMeasuredDimension(mWidth, mHeight)
  }

  override fun onDraw(canvas: Canvas) {
    super.onDraw(canvas)
    drawMultipleVerticalText(canvas)
    val leftLinePaint = mLeftLinePaint ?: return
    if (mNeedLeftLine) {
      canvas.drawLine(
        leftLinePaint.strokeWidth / 2.0f,
        dip2px(context, 1.0f)
          .toFloat(),
        leftLinePaint.strokeWidth / 2.0f,
        mMaxHeight.toFloat(),
        leftLinePaint
      )
    }
  }

  /**
   * [charHeight] default padding
   */
  private fun getNormalCharPaddingTop(charHeight: Float): Float {
    return if (mNormalCharPaddingTop != 0) {
      mNormalCharPaddingTop.toFloat()
    } else charHeight
  }

  private fun isLastCharEnglish(i: Int): Boolean {
    val text = mText ?: return false
    return i > 1 && isEnglishChar(text[i - 1])
  }

  private fun drawMultipleVerticalText(canvas: Canvas) {
    val text = mText ?: return
    val paint = mPaint ?: return
    var mTextPosY = 0
    var mTextPosX = mWidth - mLineWidth + mLineWidth / 2
    var i = 0
    while (i < text.length) {
      val ch = text[i]
      if (mLastCanShowCharIndex == 0 || i != mLastCanShowCharIndex) {
        if (i == 0 && mNeedSepLine) {
          mTextPosY += 99
          canvas.drawLine(mTextPosX.toFloat(), 0.0f, mTextPosX.toFloat(), 90.0f, paint)
        }
        if (ch == '\n') {
          mTextPosX = mTextPosX - mLineWidth - mLineSpace
          mTextPosY = 0
        } else {
          val isLastCharEnglish = isLastCharEnglish(i)
          val isSpecialChar = isSpecialChar(isLastCharEnglish, ch)
          val charHeight = getCharHeight(ch, isLastCharEnglish)
          if (mTextPosY == 0 || i == 0) {
            mTextPosY =
              ((if (isSpecialChar) 3.0f else getNormalCharPaddingTop(3.0f)) + mTextPosY.toFloat()).toInt()
          }
          // diff between char
          if (i > 0) {
            val isLastSpecialChar = isSpecialChar(isLastCharEnglish, text[i - 1])
            if (isSpecialChar && !isLastSpecialChar) {
              mTextPosY += dip2px(context, CHINESE_ENGLISH_CHAR_PADDING)
            }
          }
          mTextPosY = (mTextPosY.toFloat() + charHeight).toInt()
          if (i > 0 && isEnglishChar(ch) && mTextPosY.toFloat() + charHeight > mHeight.toFloat()) {
            if (isEnglishChar(text[i - 1])) {
              canvas.drawLine(
                mTextPosX.toFloat(),
                3.0f + (mTextPosY.toFloat() - charHeight),
                mTextPosX.toFloat(),
                (mTextPosY - 3).toFloat(),
                paint
              )
            }
            mTextPosX = mTextPosX - mLineWidth - mLineSpace
            i--
            mTextPosY = 0
          } else if (mTextPosY > mHeight) {
            mTextPosX = mTextPosX - mLineWidth - mLineSpace
            i--
            mTextPosY = 0
          } else if (isSpecialChar) {
            canvas.save()
            canvas.translate(mTextPosX.toFloat(), mTextPosY.toFloat())
            canvas.rotate(90.0f)
            paint.textSize = mTextSize + dip2px(
              context, 1.0f
            ).toFloat()
            canvas.drawText(
              ch.toString(),
              -(charHeight / 2f),
              (mLineWidth / 3).toFloat(),
              paint
            )
            paint.textSize = mTextSize.toFloat()
            canvas.restore()
            mTextPosY += ENGLISH_CHAR_PADDING
          } else if (ch != ' ') {
            canvas.drawText(
              ch.toString(),
              mTextPosX.toFloat(),
              mTextPosY.toFloat() - mFontBaselinePadding,
              paint
            )
            mTextPosY += mNormalCharPadding
          }
        }
        i++
      } else if (mNeedCollipseEnd) {
        val sepPaint = mSepPaint ?: return
        if (mNormalCharPaddingTop < 0) {
          mTextPosY -= mNormalCharPaddingTop * 2
        }
        sepPaint.style = Paint.Style.FILL
        val dotSize = paint.textSize / 39.0f * 6.0f
        val posY =
          ((if (isLastCharEnglish(i)) dotSize else 0.0f) + mTextPosY.toFloat()).toInt()
        canvas.drawCircle(mTextPosX.toFloat(), posY.toFloat(), dotSize / 2.0f, sepPaint)
        canvas.drawCircle(
          mTextPosX.toFloat(),
          posY.toFloat() + 2.0f * dotSize,
          dotSize / 2.0f,
          sepPaint
        )
        canvas.drawCircle(
          mTextPosX.toFloat(),
          posY.toFloat() + 4.0f * dotSize,
          dotSize / 2.0f,
          sepPaint
        )
        sepPaint.style = Paint.Style.STROKE
        return
      } else {
        return
      }
    }
  }

  private fun measureWidth(): Int {
    val text = mText ?: return 0
    if (TextUtils.isEmpty(mText)) {
      return 0
    }
    var h = 0
    var lineSpaceCount = 0
    measureLineWidth()
    measureFontHeight()
    var realLine = 1
    val contentLength = text.length
    var i = 0
    while (i < contentLength) {
      if (i == 0 && mNeedSepLine) {
        h = h + DOUBLE_SEP_LINE_HEIGHT + DOUBLE_SPE_LINE_PADDING
      }
      val ch = text[i]
      if (ch == '\n') {
        if (h > mMaxHeight) {
          mMaxHeight = h
        }
        if (i == contentLength || mMaxLines != 0 && realLine == mMaxLines) {
          break
        }
        realLine++
        lineSpaceCount++
        h = 0
      } else {
        val isLastCharEnglish = isLastCharEnglish(i)
        val isSpecialChar = isSpecialChar(isLastCharEnglish, ch)
        val charHeight = getCharHeight(ch, isLastCharEnglish)
        if (h == 0 || i == 0) {
          h = ((if (isSpecialChar) 3.0f else getNormalCharPaddingTop(3.0f)) + h.toFloat()).toInt()
        }
        h = (h.toFloat() + charHeight).toInt()
        val isEnglishChar = isEnglishChar(ch)
        if (mCustomMaxHeight > 0) {
          // diff between char
          if (i > 0) {
            val isLastEnglishChar = isEnglishChar(text[i - 1])
            if (!isLastEnglishChar && isEnglishChar) {
              h += dip2px(context, CHINESE_ENGLISH_CHAR_PADDING)
            }
          }
          if (isEnglishChar) {
            if (h.toFloat() + charHeight > mHeight.toFloat() && h.toFloat() + charHeight < mCustomMaxHeight.toFloat()) {
              mHeight = (h.toFloat() + charHeight + 1.0f).toInt()
            }
          } else if (h > mHeight && h < mCustomMaxHeight) {
            mHeight = h + 1
          }
        }
        if ((i <= 0 || !isEnglishChar || h.toFloat() + charHeight <= mHeight.toFloat()) && h <= mHeight) {
          if (isSpecialChar) {
            h += ENGLISH_CHAR_PADDING
          } else if (ch != ' ') {
            h += mNormalCharPadding
          }
          if (i == text.length - 1 && h > mMaxHeight) {
            mMaxHeight = h
          }
        } else {
          if (h.toFloat() > mMaxHeight.toFloat() + charHeight) {
            mMaxHeight = (h.toFloat() - charHeight).toInt()
          }
          if (mMaxLines == 0 || realLine != mMaxLines) {
            realLine++
            lineSpaceCount++
            i--
            h = 0
          } else if (mNeedCollipseEnd) {
            mLastCanShowCharIndex = i - 1
          } else {
            mLastCanShowCharIndex = i
          }
        }
      }
      i++
    }
    if (mOnRealLineChangeListener != null) {
      mOnRealLineChangeListener!!.realLineChange(realLine)
    }
    val i2 = mLineSpace * lineSpaceCount + mLineWidth * realLine
    val dip2px = if (mNeedLeftLine) {
      dip2px(context, 10.0f)
    } else {
      0
    }
    return dip2px + i2
  }

  private fun getCharHeight(ch: Char, isLastCharEnglish: Boolean): Float {
    val isEnglishChar = isSpecialChar(isLastCharEnglish, ch)
    if (ch == ' ') {
      // set blank space height
      return dip2px(context, 4.0f).toFloat()
    }
    if (!isEnglishChar) {
      return mFontHeight.toFloat()
    }
    val paint = mPaint ?: return 0f
    val space = FloatArray(1)
    paint.getTextWidths(ch.toString() + "", space)
    return space[0]
  }

  private fun isSpecialChar(lastCharIsEnglish: Boolean, ch: Char): Boolean {
    val alwaysTransAfterEng = mTransAfterEngChars ?: return false
    return ch >= 'a' && ch <= 'z' || (
      ch >= 'A' && ch <= 'Z' || lastCharIsEnglish && alwaysTransAfterEng.contains(
        ch.toString() + ""
      ) || alwaysTransAfterEng.contains(ch.toString() + "") ||
        ch.toInt() > 127 && ch.toInt() < 256
      )
  }

  private fun isEnglishChar(ch: Char): Boolean {
    return ch >= 'a' && ch <= 'z' || ch >= 'A' && ch <= 'Z'
  }

  override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
    super.onLayout(changed, left, top, right, bottom)
  }

  private fun measureLineWidth() {
    val paint = mPaint ?: return
    if (mLineWidth == 0) {
      val widths = FloatArray(1)
      paint.getTextWidths("正", widths)
      mLineWidth = Math.ceil(widths[0].toDouble()).toInt()
    }
  }

  private fun measureFontHeight() {
    val paint = mPaint ?: return
    val fm = paint.fontMetrics
    mFontHeight = Math.ceil((fm.bottom - fm.top).toDouble()).toInt()
    mFontBaselinePadding = fm.bottom
  }

  companion object {
    private const val DOUBLE_SEP_LINE_HEIGHT = 90
    private const val DOUBLE_SPE_LINE_PADDING = 9
    private const val DEFAULT_TEXT_SIZE = 15
    private const val DEFAULT_COLOR = Color.BLACK
    private const val DEFAULT_CUSTOM_MAX_HEIGHT = Int.MAX_VALUE
    private const val DEFAULT_MAX_LINES = 7
    private const val DEFAULT_NORMAL_CHAR_PADDINGTOP = 0
    private const val DEFAULT_NORMAL_CHAR_PADDING = 0
    private const val DEFAULT_LINE_SPACE = 0
    fun dip2px(context: Context, dpValue: Float): Int {
      return TypedValue.applyDimension(1, dpValue, context.resources.displayMetrics)
        .toInt()
    }
  }

  init {
    init()
  }
}
