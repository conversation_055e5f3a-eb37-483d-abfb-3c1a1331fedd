package com.ricepo.style.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.core.widget.NestedScrollView

//
// Created by <PERSON><PERSON> on 2/19/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class LockableNestedScrollView : NestedScrollView {

  // by default is scrollable
  private var scrollable = true

  constructor(context: Context) : super(context) {}
  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {}
  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
    context,
    attrs,
    defStyleAttr
  ) {
  }

  override fun onTouchEvent(ev: MotionEvent): Boolean {
    return scrollable && super.onTouchEvent(ev)
  }

  override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
    return scrollable && super.onInterceptTouchEvent(ev)
  }

  fun setScrollingEnabled(enabled: <PERSON>olean) {
    scrollable = enabled
  }
}
