package com.ricepo.style.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.style.ImageSpan
import androidx.annotation.NonNull

//
// Created by <PERSON><PERSON> on 13/8/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

/**
 * the image height < text height
 */
class CenterAlignImageSpan : ImageSpan {

  /**
   * translate y offset, default translate
   */
  var isOffset: Boolean = true

  constructor(drawable: Drawable) : super(drawable) {
  }

  constructor(drawable: Drawable, verticalAlignment: Int) : super(drawable, verticalAlignment) {
  }

  constructor(context: Context, bitmap: Bitmap) : super(context, bitmap) {
  }

  override fun draw(
    @NonNull canvas: Canvas,
    text: CharSequence?,
    start: Int,
    end: Int,
    x: Float,
    top: Int,
    y: Int,
    bottom: Int,
    @NonNull paint: Paint
  ) {
    if (isOffset) {
      val b = drawable
      val fm: Paint.FontMetricsInt = paint.fontMetricsInt
      val transY: Float = (y + fm.descent + y + fm.ascent) / 2 - b.bounds.bottom / 2f
//            val transY: Float = (y + fm.descent + y + fm.top) / 2 - b.bounds.bottom / 2f
      canvas.save()
      canvas.translate(x, transY)
      b.draw(canvas)
      canvas.restore()
    } else {
      super.draw(canvas, text, start, end, x, top, y, bottom, paint)
    }
  }
}
