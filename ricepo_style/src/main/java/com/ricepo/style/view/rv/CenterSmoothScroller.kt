package com.ricepo.style.view.rv

import android.content.Context
import android.util.DisplayMetrics

//
// Created by <PERSON><PERSON> on 1/29/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class CenterSmoothScroller(
  context: Context,
  private val msPerInch: Float = 0f
) : SmoothScroller(context) {

  override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
    return if (msPerInch > 0) {
      msPerInch / displayMetrics.densityDpi
    } else {
      super.calculateSpeedPerPixel(displayMetrics)
    }
  }

  override fun calculateDtToFit(
    viewStart: Int,
    viewEnd: Int,
    boxStart: Int,
    boxEnd: Int,
    snapPreference: Int
  ): Int {
    return (boxStart + (boxEnd - boxStart) / 2) - (viewStart + (viewEnd - viewStart) / 2)
  }
}
