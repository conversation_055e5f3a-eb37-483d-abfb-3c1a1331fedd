package com.ricepo.style.view

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import androidx.core.widget.NestedScrollView

//
// Created by <PERSON><PERSON> on 3/25/21.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
class StateNestedScrollView : NestedScrollView {
  constructor(context: Context) : super(context) {}
  constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {}
  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
    context,
    attrs,
    defStyleAttr
  ) {}

  private var mIsTouched = false
  private var mScrollState = OnScrollListener.SCROLL_STATE_IDLE
  private var mOnScrollListener: OnScrollListener? = null

  private val mHandler = Handler(
    Looper.getMainLooper(),
    object : Handler.Callback {
      private var mLastY = Int.MIN_VALUE
      override fun handleMessage(msg: Message): Boolean {
        if (msg.what == MSG_SCROLL) {
          val scrollY = scrollY
          log("handleMessage, lastY = $mLastY, y = $scrollY")
          if (!mIsTouched && mLastY == scrollY) {
            mLastY = Int.MIN_VALUE
            setScrollState(OnScrollListener.SCROLL_STATE_IDLE)
          } else {
            mLastY = scrollY
            restartCheckStopTiming()
          }
          return true
        }
        return false
      }
    }
  )

  private fun restartCheckStopTiming() {
    mHandler.removeMessages(MSG_SCROLL)
    mHandler.sendEmptyMessageDelayed(MSG_SCROLL, CHECK_SCROLL_STOP_DELAY_MILLIS.toLong())
  }

  fun setOnScrollListener(onScrollListener: OnScrollListener?) {
    mOnScrollListener = onScrollListener
  }

  override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
    handleDownEvent(ev)
    return super.onInterceptTouchEvent(ev)
  }

  override fun onTouchEvent(ev: MotionEvent): Boolean {
    handleUpEvent(ev)
    return super.onTouchEvent(ev)
  }

  override fun onScrollChanged(l: Int, t: Int, oldl: Int, oldt: Int) {
    super.onScrollChanged(l, t, oldl, oldt)
    log(
      String.format(
        "onScrollChanged, isTouched = %s, l: %d --> %d, t: %d --> %d",
        mIsTouched,
        oldl,
        l,
        oldt,
        t
      )
    )
    if (mIsTouched) {
      setScrollState(OnScrollListener.SCROLL_STATE_TOUCH_SCROLL)
    } else {
      setScrollState(OnScrollListener.SCROLL_STATE_FLING)
      restartCheckStopTiming()
    }
    if (mOnScrollListener != null) {
      mOnScrollListener?.onScroll(this, mIsTouched, l, t, oldl, oldt)
    }
  }

  private fun handleDownEvent(ev: MotionEvent) {
    when (ev.action) {
      MotionEvent.ACTION_DOWN -> {
        log("handleEvent, action = " + ev.action)
        mIsTouched = true
      }
    }
  }

  private fun handleUpEvent(ev: MotionEvent) {
    when (ev.action) {
      MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
        log("handleEvent, action = " + ev.action)
        mIsTouched = false
        restartCheckStopTiming()
      }
    }
  }

  private fun setScrollState(state: Int) {
    if (mScrollState != state) {
      log(String.format("---- onScrollStateChanged, state: %d --> %d", mScrollState, state))
      mScrollState = state
      if (mOnScrollListener != null) {
        mOnScrollListener?.onScrollStateChanged(this, state)
      }
    }
  }

  private fun log(obj: String) {
    if (DEBUG) {
      Log.d(javaClass.simpleName, obj)
    }
  }

  companion object {
    private const val DEBUG = false
    private const val CHECK_SCROLL_STOP_DELAY_MILLIS = 80
    private const val MSG_SCROLL = 1
  }

  interface OnScrollListener {
    fun onScrollStateChanged(view: StateNestedScrollView?, scrollState: Int)
    fun onScroll(
      view: StateNestedScrollView?,
      isTouchScroll: Boolean,
      l: Int,
      t: Int,
      oldl: Int,
      oldt: Int
    )

    companion object {
      /**
       * The view is not scrolling. Note navigating the list using the trackball counts as
       * being in the idle state since these transitions are not animated.
       */
      const val SCROLL_STATE_IDLE = 0

      /**
       * The user is scrolling using touch, and their finger is still on the screen
       */
      const val SCROLL_STATE_TOUCH_SCROLL = 1

      /**
       * The user had previously been scrolling using touch and had performed a fling. The
       * animation is now coasting to a stop
       */
      const val SCROLL_STATE_FLING = 2
    }
  }
}
