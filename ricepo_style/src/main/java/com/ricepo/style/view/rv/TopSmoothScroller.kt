package com.ricepo.style.view.rv

import android.content.Context
import android.util.DisplayMetrics

//
// Created by <PERSON><PERSON> on 2/11/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class TopSmoothScroller(
  context: Context,
  private val msPerInch: Float = 0f
) : SmoothScroller(context) {

  override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
    // default ms per inch 25
    return if (msPerInch > 0) {
      msPerInch / displayMetrics.densityDpi
    } else {
      super.calculateSpeedPerPixel(displayMetrics)
    }
  }

  override fun getHorizontalSnapPreference(): Int {
    return SNAP_TO_START
  }

  override fun getVerticalSnapPreference(): Int {
    return SNAP_TO_START
  }
}
