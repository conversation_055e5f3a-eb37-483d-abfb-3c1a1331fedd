package com.ricepo.style.sheet

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.ricepo.style.R
import com.ricepo.style.ResourcesUtil

//
// Created by <PERSON><PERSON> on 3/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//

class BaseBottomSheetFragment<T>(
  private val listData: List<String> = listOf(),
  private val listModel: List<T> = listOf(),
  private val title: String? = null
) :
  RoundedBottomSheetDialogFragment(), View.OnClickListener {

  companion object {
    fun <T> newInstance(listData: List<String>, listModel: List<T> = listOf(), title: String? = null):
      BaseBottomSheetFragment<T> {
      return BaseBottomSheetFragment(listData, listModel, title)
    }
  }

  /**
   * need to implements OnItemClickListener to construct
   */
  var onItemClickListener: OnItemClickListener<T>? = null

  var onItemTextClickListener: OnItemTextClickListener? = null

  var onSheetDetach: () -> Unit = {}

  private var titleView: TextView? = null
  private var titleDividerView: TextView? = null
  private var sheetContainerLayout: LinearLayout? = null

  override fun onCreateView(
    inflater: LayoutInflater,
    container: ViewGroup?,
    savedInstanceState: Bundle?
  ): View? {
    val view = layoutInflater.inflate(R.layout.fragment_bottom_sheet, null)
    sheetContainerLayout = view.findViewById(R.id.sheet_container)
    titleView = view.findViewById(R.id.tv_sheet_title)
    titleDividerView = view.findViewById(R.id.tv_divider_title)
    setTitle(title)
    return view
  }

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)

    // add cancel
    var datas = listData.toMutableList()
    datas.add(ResourcesUtil.getString(com.ricepo.style.R.string.cancel))

    datas?.map {
      val tv = TextView(context)
      tv.height = resources.getDimension(R.dimen.sw_67dp).toInt()
      tv.gravity = Gravity.CENTER_VERTICAL
      tv.setPadding(
        resources.getDimensionPixelOffset(R.dimen.sw_26dp),
        0, 0, 0
      )
      tv.setOnClickListener(this)
      tv.text = it
      tv.setTextColor(ResourcesUtil.getColor(R.color.mainText, view.context))
      sheetContainerLayout?.addView(tv)

      val tvLine = TextView(context)
      tvLine.height = resources.getDimensionPixelOffset(R.dimen.sw_1dp)
      tvLine.setBackgroundColor(ResourcesUtil.getColor(R.color.dividerShadow, view.context))

      sheetContainerLayout?.addView(tvLine)
    }
  }

  override fun onAttach(context: Context) {
    super.onAttach(context)
    if (context is OnItemTextClickListener) {
      onItemTextClickListener = context as OnItemTextClickListener
    }
  }

  override fun onDetach() {
    super.onDetach()
    onSheetDetach()
    onItemTextClickListener = null
  }

  override fun onClick(v: View?) {
    val tv = v as TextView
    val text = tv.text.toString()

    if (ResourcesUtil.getString(com.ricepo.style.R.string.cancel) != text) {
      val index = listData.indexOf(text)
      var data: T? = null
      if (index > -1 && listModel.isNotEmpty() && index < listModel.size) {
        data = listModel[index]
      }
      onItemClickListener?.onItemClick(text, data)
      onItemTextClickListener?.onItemClick(text)
    }
    // close bottom sheet dialog
    dismiss()
  }

  /**
   * don't display title on external calls
   */
  private fun setTitle(title: String?) {

    if (title.isNullOrEmpty()) return

    titleView?.text = title
    titleView?.visibility = View.VISIBLE
    titleDividerView?.visibility = View.VISIBLE
  }

//    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
//        val c = context
//        if (c != null) {
//            return FixHeightBottomSheetDialog(c)
//        }
//        return super.onCreateDialog(savedInstanceState)
//    }

  interface OnItemClickListener<T> {
    fun onItemClick(text: String, data: T?)
  }

  interface OnItemTextClickListener {
    fun onItemClick(text: String)
  }
}
