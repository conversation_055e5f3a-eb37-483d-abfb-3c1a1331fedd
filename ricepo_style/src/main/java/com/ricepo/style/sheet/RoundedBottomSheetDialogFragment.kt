package com.ricepo.style.sheet

import android.os.Bundle
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.ricepo.style.R

//
// Created by <PERSON><PERSON> on 10/8/2021.
// Copyright (c) 2021 Ricepo LLC. All rights reserved.
//
open class RoundedBottomSheetDialogFragment : BottomSheetDialogFragment() {

  override fun getTheme(): Int = R.style.CustomBottomSheetDialog

  override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    view.setBackgroundResource(R.drawable.rounded_bottom_sheet)
  }

  fun expendDialog(logTag: String, performOnError: () -> Unit) {
    try {
      val bottomSheet = dialog?.findViewById(com.google.android.material.R.id.design_bottom_sheet) as View
      val behavior = BottomSheetBehavior.from(bottomSheet)
      val displayMetrics = DisplayMetrics()
      requireActivity().windowManager?.defaultDisplay?.getMetrics(displayMetrics)
      behavior.peekHeight = displayMetrics.heightPixels
    } catch (e: NullPointerException) {
      Log.d(logTag, e.message ?: "NPE in onResume")
      performOnError()
    }
  }
}
