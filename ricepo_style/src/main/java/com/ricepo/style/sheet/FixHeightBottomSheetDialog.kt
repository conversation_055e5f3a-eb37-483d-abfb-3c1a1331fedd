package com.ricepo.style.sheet

import android.content.Context
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog

//
// Created by <PERSON><PERSON> on 28/6/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class FixHeightBottomSheetDialog : BottomSheetDialog {

  private var mContentView: View? = null

  constructor(context: Context) : super(context) {}

  constructor(context: Context, theme: Int) : super(context, theme) {}

  override fun onStart() {
    super.onStart()
    fixHeight()
  }

  override fun setContentView(view: View) {
    super.setContentView(view)
    mContentView = view
  }

  override fun setContentView(view: View, params: ViewGroup.LayoutParams?) {
    super.setContentView(view, params)
    mContentView = view
  }

  private fun fixHeight() {
    if (null == mContentView) {
      return
    }
    val parent = mContentView!!.parent as View
    val behavior: BottomSheetBehavior<*> = BottomSheetBehavior.from(parent)
    mContentView!!.measure(0, 0)
    behavior.peekHeight = mContentView!!.measuredHeight
    val params =
      parent.layoutParams as CoordinatorLayout.LayoutParams
    params.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
    parent.layoutParams = params
  }
}
