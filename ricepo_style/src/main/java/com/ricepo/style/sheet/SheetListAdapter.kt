package com.ricepo.style.sheet

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

//
// Created by <PERSON><PERSON> on 3/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
class SheetListAdapter(val context: Context) : RecyclerView.Adapter<SheetListAdapter.ViewHolder>() {

  inner class ViewHolder(val view: TextView) : RecyclerView.ViewHolder(view) {

    fun bind(listener: View.OnClickListener?, item: String) {
      view.text = item
    }
  }

  var items: List<String> = mutableListOf()

  override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
//        val inflater = parent.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE)
//        return ViewHolder(inflater.from)
    val tv = TextView(context)
    tv.height = 100
    return ViewHolder(tv)
  }

  override fun getItemCount(): Int {
    return items.size
  }

  override fun onBindViewHolder(holder: ViewHolder, position: Int) {
    val im = items[position]
    holder.bind(null, im)
  }
}
