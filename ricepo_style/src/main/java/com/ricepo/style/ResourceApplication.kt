package com.ricepo.style

import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources

//
// Created by <PERSON><PERSON> on 23/4/2020.
// Copyright (c) 2020 Ricepo LLC. All rights reserved.
//
open class ResourceApplication : Application() {

  companion object {
    lateinit var context: Context

    fun getResources(): Resources {
      context = LocaleUtil.setLocale(this.context)
      val resources = context.resources
      DisplayUtil.setDensity(resources)
      return resources
    }

    /**
     * reset context by language
     */
    fun resetContext(lang: String?) {
      context = LocaleUtil.changeApplicationLocale(context, lang)
    }
  }

  override fun onCreate() {
    super.onCreate()
    context = applicationContext
  }

  override fun attachBaseContext(base: Context) {
    context = base
    super.attachBaseContext(LocaleUtil.setLocale(base))
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    LocaleUtil.setLocale(applicationContext)
    LocaleUtil.changeApplicationLocale(applicationContext)
  }
}
