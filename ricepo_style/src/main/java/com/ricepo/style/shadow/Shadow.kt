package com.ricepo.style.shadow

import androidx.annotation.ColorRes

interface Shadow {
  // shadow radius
  fun setShadowRadius(radius: Float): Shadow?

  // shadow radius by unit
  fun setShadowRadius(unit: Int, radius: Float): Shadow?

  // shadow color
  fun setShadowColor(color: Int): Shadow?

  // shadow color by resource id
  fun setShadowColorRes(@ColorRes colorRes: Int): Shadow?

  /**
   * shadow blur radius
   * @param radius
   */
  fun setBlurRadius(radius: Float): Shadow?

  /**
   *
   * @param unit @[android.util.TypedValue.TYPE_DIMENSION]
   * @param radius blur shadow radius
   */
  fun setBlurRadius(unit: Int, radius: Float): Shadow?

  /**
   * horizontal offset
   * @param offset
   */
  fun setXOffset(offset: Float): Shadow?

  /**
   * horizontal offset by unit
   * @param unit @[android.util.TypedValue.TYPE_DIMENSION]
   * @param offset
   */
  fun setXOffset(unit: Int, offset: Float): Shadow?

  /**
   * vertical offset
   * @param offset
   */
  fun setYOffset(offset: Float): Shadow?

  /**
   * vertical offset by unit
   * @param unit @[android.util.TypedValue.TYPE_DIMENSION]
   * @param offset
   */
  fun setYOffset(unit: Int, offset: Float): Shadow?

  /**
   * draw commit
   */
  fun commit()
}
