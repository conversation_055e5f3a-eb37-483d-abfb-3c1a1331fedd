package com.ricepo.style.shadow

import android.content.Context
import android.content.res.TypedArray
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.graphics.drawable.BitmapDrawable
import android.util.AttributeSet
import android.widget.FrameLayout
import com.ricepo.style.R
import com.ricepo.style.ResourcesUtil

class LuckyShadowLayout @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
  // feeling lucky view added flag
  var isAdded = false

  private var mShadowColor = 0
  private var mShadowLength = 0f
  private var mShadowRadius = 0f
  private var shadowTranslationX = 0f
  private var shadowTranslationY = 0f
  private var mInvalidateShadowOnSizeChanged = true
  private var mForceInvalidateShadow = false
  private var shadowDirection = ALL
  private var luckyLeft = 0
  private var luckyTop = 0
  private var luckyRight = 0
  private var luckyBottom = 0

  var isShowShadow = true

  private fun initView(context: Context, attrs: AttributeSet?) {
    initAttributes(context, attrs)
    val xPadding = (mShadowLength + Math.abs(shadowTranslationX)).toInt()
    val yPadding = (mShadowLength + Math.abs(shadowTranslationY)).toInt()
    if (shadowDirection and LEFT == LEFT) {
      luckyLeft = xPadding
    }
    if (shadowDirection and TOP == TOP) {
      luckyTop = yPadding
    }
    if (shadowDirection and RIGHT == RIGHT) {
      luckyRight = xPadding
    }
    if (shadowDirection and BOTTOM == BOTTOM) {
      luckyBottom = yPadding
    }
    setPadding(luckyLeft, luckyTop, luckyRight, luckyBottom)
  }

  private fun initAttributes(context: Context, attrs: AttributeSet?) {
    val attr = getTypedArray(context, attrs, R.styleable.LuckyShadowLayout) ?: return
    try {
      mShadowRadius = attr.getDimension(R.styleable.LuckyShadowLayout_luckyShadowRadius, 0f)
      mShadowLength = attr.getDimension(R.styleable.LuckyShadowLayout_luckyShadowLength, 4f)
      shadowTranslationX = attr.getDimension(R.styleable.LuckyShadowLayout_luckyShadowTranslationX, 0f)
      shadowTranslationY = attr.getDimension(R.styleable.LuckyShadowLayout_luckyShadowTranslationY, 0f)
      mShadowColor =
        attr.getColor(R.styleable.LuckyShadowLayout_luckyShadowColor, Color.parseColor("#b0333333"))
      shadowDirection = attr.getInt(R.styleable.LuckyShadowLayout_luckyShadowDirection, ALL)
    } finally {
      attr.recycle()
    }
  }

  private fun getTypedArray(
    context: Context,
    attributeSet: AttributeSet?,
    attr: IntArray
  ): TypedArray {
    return context.obtainStyledAttributes(attributeSet, attr, 0, 0)
  }

  override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
    super.onSizeChanged(w, h, oldw, oldh)
    if (w > 0 && h > 0 && (background == null || mInvalidateShadowOnSizeChanged || mForceInvalidateShadow)) {
      mForceInvalidateShadow = false
      setBackgroundCompat(w, h)
    }
  }

  override fun getSuggestedMinimumHeight(): Int {
    return 0
  }

  override fun getSuggestedMinimumWidth(): Int {
    return 0
  }

  private fun setBackgroundCompat(w: Int, h: Int) {
    val bitmap = createShadowBitmap(
      w,
      h,
      mShadowRadius,
      mShadowLength,
      shadowTranslationX,
      shadowTranslationY,
      mShadowColor,
      Color.TRANSPARENT
    )
    val drawable = BitmapDrawable(resources, bitmap)
    background = drawable
  }

  private fun createShadowBitmap(
    shadowWidth: Int,
    shadowHeight: Int,
    cornerRadius: Float,
    shadowRadius: Float,
    dx: Float,
    dy: Float,
    shadowColor: Int,
    fillColor: Int
  ): Bitmap {
    val output = Bitmap.createBitmap(shadowWidth, shadowHeight, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(output)
    val shadowRect = RectF(
      shadowRadius,
      shadowRadius,
      shadowWidth - shadowRadius,
      shadowHeight - shadowRadius
    )
    if (dy > 0) {
      shadowRect.top += dy
      shadowRect.bottom -= dy
    } else if (dy < 0) {
      shadowRect.top += Math.abs(dy)
      shadowRect.bottom -= Math.abs(dy)
    }
    if (dx > 0) {
      shadowRect.left += dx
      shadowRect.right -= dx
    } else if (dx < 0) {
      shadowRect.left += Math.abs(dx)
      shadowRect.right -= Math.abs(dx)
    }
    val shadowPaint = Paint()
    shadowPaint.isAntiAlias = true
    shadowPaint.color = fillColor
    shadowPaint.style = Paint.Style.FILL
    if (!isInEditMode && isShowShadow) {
      shadowPaint.setShadowLayer(shadowRadius, dx, dy, shadowColor)
    }
    canvas.drawRoundRect(shadowRect, cornerRadius, cornerRadius, shadowPaint)
    return output
  }

  override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
    super.onLayout(changed, left, top, right, bottom)
    if (mForceInvalidateShadow) {
      mForceInvalidateShadow = false
      setBackgroundCompat(right - left, bottom - top)
    }
  }

  fun setInvalidateShadowOnSizeChanged(invalidateShadowOnSizeChanged: Boolean) {
    mInvalidateShadowOnSizeChanged = invalidateShadowOnSizeChanged
  }

  fun setShadowColor(colorId: Int) {
    mShadowColor = ResourcesUtil.getColor(colorId, this)
    invalidateShadow()
  }

  fun setShadowLength(mShadowRadius: Float) {
    mShadowLength = mShadowRadius
    invalidateShadow()
  }

  fun invalidateShadow() {
    mForceInvalidateShadow = true
    requestLayout()
    invalidate()
  }

  companion object {
    const val ALL = 0x1111
    const val LEFT = 0x0001
    const val TOP = 0x0010
    const val RIGHT = 0x0100
    const val BOTTOM = 0x1000
  }

  init {
    initView(context, attrs)
  }
}
