package com.ricepo.style.shadow

import android.content.Context
import android.content.res.Resources
import android.graphics.BlurMaskFilter
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.util.TypedValue
import androidx.constraintlayout.widget.ConstraintLayout
import com.ricepo.style.R

class ShadowLayout : ConstraintLayout {
  private var shadowColor = Color.parseColor("#333333")

  private val TYPE_NEIGHBORING = 1
  // show type, 0: default single 1: single 2: neighboring 3: all side
  private var shadowType = 0

  private var shadowRadius = 0f
  private var blurRadius = SHADOW_DEFAULT_BLUR_RADIUS
  private var xOffset = dp2px(10)
  private var yOffset = dp2px(10)
  private var bgColor = Color.WHITE
  private var hasEffect = false

  var leftPadding = 0
  var rightPadding = 0
  var topPadding = 0
  var bottomPadding = 0

  val shadowConfig: Shadow = ShadowConfig(this)
  private var mWidthMode = 0f
  private var mHeightMode = 0f
  private val mPaint = Paint()
  private val locationPaint = Paint()

  constructor(context: Context) : super(context, null) {}

  @JvmOverloads
  constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) :
    super(context, attrs, defStyleAttr) {
      // cancel hardware acceleration
      setLayerType(LAYER_TYPE_SOFTWARE, null)
      val typedArray = context.obtainStyledAttributes(attrs, R.styleable.ShadowLayout)
      shadowColor = typedArray.getColor(R.styleable.ShadowLayout_shadowColor, Color.BLUE)
      blurRadius =
        typedArray.getDimension(R.styleable.ShadowLayout_blurRadius, SHADOW_DEFAULT_BLUR_RADIUS)
      shadowRadius = typedArray.getDimension(R.styleable.ShadowLayout_shadowRadius, 0f)
      hasEffect = typedArray.getBoolean(R.styleable.ShadowLayout_hasEffect, false)
      xOffset = typedArray.getDimension(R.styleable.ShadowLayout_xOffset, dp2px(10))
      yOffset = typedArray.getDimension(R.styleable.ShadowLayout_yOffset, dp2px(10))
      bgColor = typedArray.getColor(R.styleable.ShadowLayout_bgColor, Color.WHITE)
      shadowType = typedArray.getInteger(R.styleable.ShadowLayout_shadowType, 0)
      typedArray.recycle()
      if (shadowRadius < 0) {
        shadowRadius = -shadowRadius
      }
      if (blurRadius < 0) {
        blurRadius = -blurRadius
      }
      blurRadius = Math.min(SHADOW_MAX_BLUR, blurRadius)
      if (Math.abs(xOffset) > SHADOW_MAX_OFFSET) {
        xOffset = xOffset / Math.abs(xOffset) * SHADOW_MAX_OFFSET
      }
      if (Math.abs(yOffset) > SHADOW_MAX_OFFSET) {
        yOffset = yOffset / Math.abs(yOffset) * SHADOW_MAX_OFFSET
      }
      setBackgroundColor(Color.parseColor("#00ffffff"))
      init()
    }

  private fun init() {
    when {
      xOffset > 0 -> {
        rightPadding = (blurRadius + Math.abs(xOffset)).toInt()
      }
      xOffset == 0f -> {
        leftPadding = blurRadius.toInt()
        rightPadding = blurRadius.toInt()
      }
      else -> {
        leftPadding = (blurRadius + Math.abs(xOffset)).toInt()
      }
    }
    when {
      yOffset > 0 -> {
        bottomPadding = (blurRadius + Math.abs(yOffset)).toInt()
      }
      yOffset == 0f -> {
        topPadding = blurRadius.toInt()
        bottomPadding = blurRadius.toInt()
      }
      else -> {
        topPadding = (blurRadius + Math.abs(yOffset)).toInt()
      }
    }
    if (shadowType == TYPE_NEIGHBORING) {
      // neighboring
      leftPadding = 0
      rightPadding = 0
    }
    setPadding(leftPadding, topPadding, rightPadding, bottomPadding)
  }

  override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
    super.onLayout(changed, l, t, r, b)
  }

  override fun dispatchDraw(canvas: Canvas) {
    super.dispatchDraw(canvas)
  }

  override fun onDraw(canvas: Canvas) {
    drawBackground(canvas)
  }

  override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
    super.onMeasure(widthMeasureSpec, heightMeasureSpec)
  }

  private fun drawBackground(canvas: Canvas) {
    mWidthMode = measuredWidth.toFloat()
    mHeightMode = measuredHeight.toFloat()
    var startX = 0f
    var startY = 0f
    var endX = 0f
    var endY = 0f
    when {
      shadowType == TYPE_NEIGHBORING -> {
        // shadow type neighboring
        startX = -blurRadius
        endX = mWidthMode + blurRadius
      }
      xOffset == 0f -> {
        startX = rightPadding.toFloat()
        endX = mWidthMode - blurRadius
      }
      else -> {
        startX = rightPadding + blurRadius
        endX = mWidthMode - leftPadding - blurRadius
      }
    }

    if (yOffset == 0f) {
      startY = bottomPadding.toFloat()
      endY = mHeightMode - blurRadius
    } else {
      startY = bottomPadding + blurRadius
      endY = mHeightMode - topPadding - blurRadius
    }

    //        mPaint.setShadowLayer(blurRadius,0,0,shadowColor);
    if (blurRadius > 0) {
      mPaint.maskFilter = BlurMaskFilter(blurRadius, BlurMaskFilter.Blur.NORMAL)
    }
    mPaint.color = shadowColor
    mPaint.isAntiAlias = true
    val shadowRect = RectF(startX, startY, endX, endY)
    val locationRectF = RectF(
      leftPadding.toFloat(), topPadding.toFloat(),
      mWidthMode - rightPadding, mHeightMode - bottomPadding
    )
    if (shadowRadius == 0f) {
      canvas.drawRect(shadowRect, mPaint)
    } else {
      canvas.drawRoundRect(shadowRect, shadowRadius, shadowRadius, mPaint)
    }
    locationPaint.color = bgColor
    locationPaint.isAntiAlias = true
    if (shadowRadius == 0f) {
      canvas.drawRect(locationRectF, locationPaint)
    } else {
      canvas.drawRoundRect(locationRectF, shadowRadius, shadowRadius, locationPaint)
    }
  }

  internal inner class ShadowConfig constructor( // proxy
    private val shadow: ShadowLayout
  ) : Shadow {
    override fun setShadowRadius(radius: Float): Shadow {
      return setShadowRadius(TypedValue.COMPLEX_UNIT_DIP, radius)
    }

    override fun setShadowRadius(unit: Int, radius: Float): Shadow {
      val c = context
      val r: Resources
      r = if (c == null) {
        Resources.getSystem()
      } else {
        c.resources
      }
      shadow.shadowRadius =
        Math.abs(TypedValue.applyDimension(unit, radius, r.displayMetrics))
      return this
    }

    override fun setShadowColor(color: Int): Shadow {
      shadow.shadowColor = color
      return this
    }

    override fun setShadowColorRes(colorRes: Int): Shadow {
      shadow.shadowColor = shadow.resources.getColor(colorRes)
      return this
    }

    override fun setBlurRadius(radius: Float): Shadow {
      return setBlurRadius(TypedValue.COMPLEX_UNIT_DIP, radius)
    }

    override fun setBlurRadius(unit: Int, radius: Float): Shadow {
      val c = context
      val r: Resources
      r = if (c == null) {
        Resources.getSystem()
      } else {
        c.resources
      }
      shadow.blurRadius = Math.min(
        SHADOW_MAX_BLUR,
        Math.abs(TypedValue.applyDimension(unit, radius, r.displayMetrics))
      )
      return this
    }

    override fun setXOffset(offset: Float): Shadow {
      return setXOffset(TypedValue.COMPLEX_UNIT_DIP, offset)
    }

    override fun setXOffset(unit: Int, offset: Float): Shadow {
      val c = context
      val r: Resources
      r = if (c == null) {
        Resources.getSystem()
      } else {
        c.resources
      }
      var x = TypedValue.applyDimension(unit, offset, r.displayMetrics)
      if (Math.abs(x) > SHADOW_MAX_OFFSET) {
        x = x / Math.abs(x) * SHADOW_MAX_OFFSET
      }
      shadow.xOffset = x
      return this
    }

    override fun setYOffset(offset: Float): Shadow {
      return setYOffset(TypedValue.COMPLEX_UNIT_DIP, offset)
    }

    override fun setYOffset(unit: Int, offset: Float): Shadow {
      val c = context
      val r: Resources
      r = if (c == null) {
        Resources.getSystem()
      } else {
        c.resources
      }
      var y = TypedValue.applyDimension(unit, offset, r.displayMetrics)
      if (Math.abs(y) > SHADOW_MAX_OFFSET) {
        y = y / Math.abs(y) * SHADOW_MAX_OFFSET
      }
      shadow.yOffset = y
      return this
    }

    override fun commit() {
      shadow.init()
      shadow.requestLayout()
      shadow.postInvalidate()
    }
  }

  companion object {
    private const val TAG = "ShadowLayout"
    val SHADOW_DEFAULT_RADIUS = dp2px(5)
    val SHADOW_MAX_OFFSET = dp2px(20)
    val SHADOW_MAX_BLUR = dp2px(20)
    val SHADOW_DEFAULT_BLUR_RADIUS = dp2px(5)
    private fun dp2px(dpValue: Int): Float {
      return 0.5f + dpValue * Resources.getSystem().displayMetrics.density
    }
  }
}
