<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:left="-100dp" android:right="-100dp" >
        <rotate
            android:layout_height="match_parent"
            android:fromDegrees="90">
            <shape
                android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="@color/dividerShadow" />
            </shape>
        </rotate>
    </item>

    <item android:left="-99dp" android:right="-100dp"
        android:bottom="0px" android:top="0px">
        <rotate android:fromDegrees="90">
            <shape android:shape="line">
                <stroke
                    android:width="1dp"
                    android:color="@color/dividerHighlight" />
            </shape>
        </rotate>
    </item>

</layer-list>