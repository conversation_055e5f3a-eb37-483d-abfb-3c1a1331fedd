<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <item >
        <shape android:shape="rectangle">
            <stroke android:color="@color/textBoxShadow" android:width="1.5dp"  />
            <corners android:radius="8dp" />
        </shape>
    </item>

    <item android:top="2dp">
        <shape android:shape="rectangle">
            <corners android:radius="8dp" />
            <padding
                android:left="5dp"
                android:top="7dp"
                android:bottom="7dp"/>
            <solid android:color="@color/textBoxFill" />
        </shape>
    </item>

<!--    <item android:bottom="-1dp" android:left="-1.2dp" android:right="-1dp" >-->
<!--        <shape android:shape="rectangle">-->
<!--            <stroke android:color="@color/textBoxShadow" android:width="1dp"  />-->
<!--            <padding-->
<!--                android:left="5dp"-->
<!--                android:top="7dp"-->
<!--                android:bottom="7dp"/>-->
<!--            <corners android:radius="8dp" />-->
<!--            <solid android:color="@color/textBoxFill" />-->
<!--        </shape>-->
<!--    </item>-->

</layer-list>