<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding android:top="0.7dp" android:bottom="1dp" />
            <stroke android:color="@color/gray8" android:width="1dp" />
            <corners android:radius="6dp" />
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item >
        <shape android:shape="rectangle">
            <padding android:top="1dp" android:bottom="0.7dp" />
            <stroke android:color="@color/white"  android:width="1dp"/>
            <corners android:radius="6dp" />
            <!-- background -->
            <solid android:color="@color/gray16" />
        </shape>
    </item>

    <!-- <PERSON>ack -->
<!--    <item>-->
<!--        <shape android:shape="rectangle">-->
<!--            <padding android:bottom="0.8dp" />-->
<!--            <stroke android:color="@color/gray8"  />-->
<!--            <corners android:radius="6dp" />-->
<!--        </shape>-->
<!--    </item>-->


    <!-- Background -->
<!--    <item>-->
<!--        <shape>-->
<!--            <solid android:color="@color/gray15" />-->
<!--            <corners android:radius="6dp" />-->
<!--        </shape>-->
<!--    </item>-->

</layer-list>