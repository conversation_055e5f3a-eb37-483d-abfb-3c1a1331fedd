<?xml version="1.0" encoding="utf-8"?>
<layer-list
    xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Top Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding android:top="1dp" />
            <solid android:color="@color/gray8"  />
            <corners android:radius="6dp" />
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item >
        <shape android:shape="rectangle">
            <padding android:top="0.8dp" android:left="0dp" android:right="0dp" android:bottom="0.2dp"/>
            <solid android:color="@color/white"  />
            <corners android:radius="6dp" />
        </shape>
    </item>

    <!-- <PERSON> Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding android:bottom="1dp" />
            <solid android:color="@color/gray8"  />
            <corners android:radius="6dp" />
        </shape>
    </item>


    <!-- Background -->
    <item>
        <shape>
            <solid android:color="@color/gray5" />
            <corners android:radius="6dp" />
        </shape>
    </item>

</layer-list>