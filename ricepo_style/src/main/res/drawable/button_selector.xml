<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
<!--    <item android:state_pressed="true">-->
<!--    </item>-->

    <item>

        <!-- radius not shadow -->
        <layer-list>

            <!-- the top white highlight -->
            <item android:bottom="4dp" android:left="1dp" android:right="1dp">
                <shape>
                    <corners android:radius="6dp" />
                    <stroke android:width="3dp" android:color="@color/second_satisfied_button_highlight" />
                </shape>
            </item>

            <!-- the bottom shadow-->
            <!-- top to remove the black edge -->
<!--            <item android:top="1dp" android:left="1.4dp" android:right="1.4dp" >-->
<!--                <shape>-->
<!--                    <corners android:bottomLeftRadius="6dp" android:bottomRightRadius="6dp"-->
<!--                        android:topLeftRadius="6dp" android:topRightRadius="6dp" />-->
<!--                    <stroke android:width="4dp" android:color="@color/second_satisfied_button_shadow" />-->
<!--                </shape>-->
<!--            </item>-->

            <!-- the side shadow left and right to remove the black edge -->
<!--            <item android:top="1dp" android:bottom="4dp" android:left="1dp" android:right="1dp">-->
<!--                <shape>-->
<!--                    <corners android:radius="8dp" />-->
<!--                    <stroke android:width="4dp"  android:color="#55afafaf" />-->
<!--                </shape>-->
<!--            </item>-->

            <!-- the content fill -->
            <item android:top="1dp" android:bottom="4dp" android:left="1.4dp" android:right="1.4dp">
                <shape>
                    <corners android:topLeftRadius="8dp" android:topRightRadius="8dp" />
                    <solid  android:color="@color/second_satisfied_button_fill" />
                </shape>
            </item>

        </layer-list>

    </item>
</selector>