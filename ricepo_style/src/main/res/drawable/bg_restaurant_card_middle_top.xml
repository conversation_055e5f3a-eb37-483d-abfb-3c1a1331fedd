<?xml version="1.0" encoding="utf-8"?>
<layer-list
    xmlns:android="http://schemas.android.com/apk/res/android">

    <item >
        <shape android:shape="rectangle">
            <stroke android:color="@color/card_border" android:width="1dp" />
            <corners android:radius="0dp" />
        </shape>
    </item>

    <item  android:bottom="0dp">
        <shape android:shape="rectangle">
            <corners android:radius="0dp" />
            <solid android:color="@color/card_top_shadow" />
        </shape>
    </item>

    <item android:top="1dp" android:bottom="0dp">
        <shape android:shape="rectangle">
            <corners android:radius="0dp" />
            <!-- background -->
            <solid android:color="@color/card_background" />
        </shape>
    </item>

</layer-list>