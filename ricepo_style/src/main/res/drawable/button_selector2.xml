<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- @android:drawable/dialog_holo_light_frame -->

    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="0dp"
                android:right="0dp"
                android:top="0dp" />
            <solid android:color="#0Dafafaf" />
            <corners android:radius="6dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="0dp"
                android:right="0dp"
                android:top="0dp" />
            <solid android:color="#10afafaf" />
            <corners android:radius="6dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="0dp"
                android:right="0dp"
                android:top="0dp" />
            <solid android:color="#15afafaf" />
            <corners android:radius="6dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="1dp"
                android:left="0dp"
                android:right="0dp"
                android:top="0dp" />
            <solid android:color="#20afafaf" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <padding
                android:bottom="2dp"
                android:left="0dp"
                android:right="0dp"
                android:top="0dp" />
            <solid android:color="#ffafafaf" />
            <corners android:radius="8dp" />
        </shape>
    </item>

    <item>
        <shape>
            <solid android:color="@color/second_satisfied_button_fill" />
            <corners android:radius="4dp" />
        </shape>
    </item>



</layer-list>