<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Top Stack -->
    <item>
        <shape android:shape="oval">
            <padding android:top="1dp" android:left="1.1dp" android:right="1.1dp"/>
            <solid android:color="@color/inputLineText" />
            <corners android:radius="60dp" />
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item>
        <shape android:shape="oval">
            <padding
                android:left="0.1dp"
                android:right="0.1dp"
                android:top="2.0dp" />
            <solid android:color="@color/luckyMenuSelectedBorder" />
            <corners android:radius="60dp" />
        </shape>
    </item>

    <item>
        <shape android:shape="oval">
            <padding android:bottom="1.5dp"/>
            <solid android:color="@color/inputLineText" />
            <corners android:radius="60dp" />
        </shape>
    </item>

    <!-- Background -->
    <item android:bottom="0.5dp">
        <shape android:shape="oval">
            <solid android:color="@color/luckyMenuSelected" />
            <corners android:radius="60dp" />
        </shape>
    </item>

</layer-list>