<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Highlight Stack -->
    <item>
        <shape android:shape="oval">
            <padding
                android:bottom="1.5dp"
                android:left="0dp"
                android:right="0dp"
                android:top="0dp" />
            <solid android:color="@color/luckyMenuShadow" />
            <corners android:radius="60dp" />
        </shape>
    </item>

    <!-- Shadow Top Stack -->
    <item>
        <shape android:shape="oval">
            <padding android:top="1.5dp" android:bottom="1dp" android:left="1dp" android:right="1dp"/>
            <solid android:color="@color/luckyMenuBorder" />
            <corners android:radius="60dp" />
<!--            <size android:height="@dimen/dip_100" android:width="@dimen/dip_100" />-->
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item>
        <shape android:shape="oval">
            <padding
                android:bottom="0dp"
                android:left="0.5dp"
                android:right="0.5dp"
                android:top="1dp" />
            <solid android:color="@color/luckyMenuShadow" />
            <corners android:radius="60dp" />
        </shape>
    </item>

    <!-- Background -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="60dp" />
        </shape>
    </item>

</layer-list>