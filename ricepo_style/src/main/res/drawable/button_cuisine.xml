<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding android:top="0dp" android:bottom="1dp" />
            <stroke android:color="#55afafaf" android:width="0.85dp" />
            <corners android:radius="10dp" />
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item >
        <shape android:shape="rectangle">
            <padding android:top="1dp" android:bottom="0.7dp" />
            <stroke android:color="@color/second_satisfied_button_highlight"  android:width="1.5dp"/>
            <corners android:radius="9dp" />
            <!-- background -->
            <solid android:color="@color/gray16" />
        </shape>
    </item>



</layer-list>