<?xml version="1.0" encoding="utf-8"?>
<layer-list
    xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding android:top="0dp" android:bottom="1dp" />
            <stroke android:color="@color/home_address_shadow" android:width="1dp" />
            <corners android:bottomLeftRadius="3dp" android:bottomRightRadius="3dp"
                android:topRightRadius="6dp" android:topLeftRadius="6dp" />
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item >
        <shape android:shape="rectangle">
            <padding android:top="0dp" android:bottom="0.7dp" />
            <stroke android:color="@color/home_address_fill"  android:width="2dp"/>
            <corners android:radius="3dp" />
            <!-- background -->
            <solid android:color="@color/home_address_fill" />
        </shape>
    </item>

</layer-list>