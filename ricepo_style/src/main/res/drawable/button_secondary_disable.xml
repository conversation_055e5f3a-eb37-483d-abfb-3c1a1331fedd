<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding android:top="0.7dp" android:bottom="1dp" />
            <stroke android:color="@color/gray15" android:width="1dp" />
            <corners android:radius="6dp" />
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item >
        <shape android:shape="rectangle">
            <padding android:top="1dp" android:bottom="0.7dp" />
            <stroke android:color="@color/gray10"  android:width="1dp"/>
            <corners android:radius="6dp" />
            <!-- background -->
            <solid android:color="@color/gray15" />
        </shape>
    </item>

<!--    &lt;!&ndash; Shadow Top Stack &ndash;&gt;-->
<!--    <item>-->
<!--        <shape android:shape="rectangle">-->
<!--            <padding android:top="1dp" />-->
<!--            <solid android:color="@color/gray15"  />-->
<!--            <corners android:radius="6dp" />-->
<!--        </shape>-->
<!--    </item>-->

<!--    &lt;!&ndash; Highlight Stack &ndash;&gt;-->
<!--    <item >-->
<!--        <shape android:shape="rectangle">-->
<!--            <padding android:top="0.8dp" android:left="0dp" android:right="0dp" android:bottom="0.2dp"/>-->
<!--            <solid android:color="@color/gray10"  />-->
<!--            <corners android:radius="6dp" />-->
<!--        </shape>-->
<!--    </item>-->

<!--    &lt;!&ndash; Shadow Boottom Stack &ndash;&gt;-->
<!--    <item>-->
<!--        <shape android:shape="rectangle">-->
<!--            <padding android:bottom="1dp" />-->
<!--            <solid android:color="@color/gray15"  />-->
<!--            <corners android:radius="6dp" />-->
<!--        </shape>-->
<!--    </item>-->


<!--    &lt;!&ndash; Background &ndash;&gt;-->
<!--    <item>-->
<!--        <shape>-->
<!--            <solid android:color="@color/gray15" />-->
<!--            <corners android:radius="6dp" />-->
<!--        </shape>-->
<!--    </item>-->

</layer-list>