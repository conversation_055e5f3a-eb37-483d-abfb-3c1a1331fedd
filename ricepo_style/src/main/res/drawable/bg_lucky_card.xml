<?xml version="1.0" encoding="utf-8"?>
<layer-list
    xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Stack -->
    <item>
        <shape android:shape="rectangle">
            <padding android:top="0dp" android:bottom="1dp" />
            <stroke android:color="@color/gray16" android:width="1dp" />
            <corners android:bottomLeftRadius="6dp" android:bottomRightRadius="6dp"
                android:topRightRadius="12dp" android:topLeftRadius="12dp" />
        </shape>
    </item>

    <!-- Highlight Stack -->
    <item >
        <shape android:shape="rectangle">
            <padding android:top="0dp" android:bottom="0.7dp" />
            <stroke android:color="@color/white"  android:width="2dp"/>
            <corners android:radius="6dp" />
            <!-- background -->
            <solid android:color="@color/gray16" />
        </shape>
    </item>

</layer-list>