<?xml version="1.0" encoding="utf-8"?>
<layer-list
    xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow Stack -->
<!--    <item>-->
<!--        <shape android:shape="rectangle">-->
<!--            <padding android:top="0dp" android:bottom="1dp" />-->
<!--            <stroke android:color="@color/rest_address_shadow" android:width="1dp" />-->
<!--            <corners android:bottomLeftRadius="8dp" android:bottomRightRadius="8dp"-->
<!--                android:topRightRadius="0dp" android:topLeftRadius="0dp" />-->
<!--        </shape>-->
<!--    </item>-->

    <!-- Highlight Stack -->
    <item >
        <shape android:shape="rectangle">
            <padding android:top="0dp" android:bottom="0.7dp" />
            <stroke android:color="@color/mr"  android:width="2dp"/>
            <corners android:bottomLeftRadius="8dp" android:bottomRightRadius="8dp"
                android:topRightRadius="0dp" android:topLeftRadius="0dp" />
            <!-- background -->
            <solid android:color="@color/mr" />
        </shape>
    </item>

</layer-list>