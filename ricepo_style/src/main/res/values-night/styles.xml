<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="EditTextStyle" parent="FontH5">
        <item name="android:shadowColor">@color/black</item>
        <item name="android:textColorHighlight">@color/gray6</item>
        <item name="android:textColor">@color/gray9</item>
        <item name="android:textCursorDrawable">@drawable/text_cursor</item>
        <item name="android:background">@drawable/edittext_bottom_line</item>
    </style>

    <!-- out of editext add text input edittext to set bottom line -->
    <style name="EditTextNoBottomLineStyle">
        <item name="android:shadowColor">@color/black</item>
        <item name="android:textColorHighlight">@color/gray6</item>
        <item name="android:textColor">@color/gray9</item>
        <item name="android:textCursorDrawable">@drawable/text_cursor</item>
        <item name="android:background">@null</item>
    </style>

    <style name="EditTextBorderStyle">
        <item name="android:shadowColor">@color/black</item>
        <item name="android:textColorHighlight">@color/gray6</item>
        <item name="android:textColor">@color/gray9</item>
        <item name="android:textCursorDrawable">@drawable/text_cursor</item>
        <item name="android:textSize">@dimen/font_size_h5</item>
        <item name="android:background">@drawable/edittext_border_line</item>
    </style>

    <style name="LuckyButtonStyle" parent="FontH6">
        <!-- opacity 40% -->
        <item name="android:textColor">#66cbcbcb</item>
    </style>

    <style name="SubLabelStyle" parent="FontH7">
        <item name="android:textColor">@color/gray7</item>
    </style>

    <style name="MenuFoodNameStyle" parent="FontH5" >
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textColor">@color/gray9</item>
    </style>

</resources>