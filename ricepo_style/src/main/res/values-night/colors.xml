<?xml version="1.0" encoding="utf-8"?>
<resources>


    <!--    new theme color-->



    <color name="fun_n1">@color/d4</color>
    <color name="fun_n2">@color/d1</color>
    <color name="fun_n3">@color/d5</color>
    <color name="fun_n4">@color/d1</color>
    <color name="fun_n5">@color/d1</color>
    <color name="fun_n6">@color/w</color>



    <color name="global_background">@color/d4</color>
    <color name="menu_banner_background">@color/d3</color>
    <color name="banner_background">@color/d3</color>
    <color name="input_divider">@color/d2</color>
    <color name="divider">@color/d6</color>
    <color name="title">@color/w</color>
    <color name="normal_text">@color/d1</color>
    <color name="card_level1">@color/d4</color>
    <color name="card_level2">@color/d5</color>

    <color name="banner_button_boarder_color">@color/gray7</color>

    <!--    new theme color-->




    <color name="green">@color/green1</color>

    <color name="colorPrimary">@color/gray3</color>
    <color name="colorPrimaryDark">@color/gray3</color>
    <color name="colorAccent">#D81B60</color>

    <color name="dividerShadow">@color/gray1</color>
    <color name="dividerHighlight">@color/gray4</color>

    <color name="mainText">@color/fun_n6</color>
    <color name="subText">@color/gray7</color>
    <color name="subTextMenu">@color/gray7</color>
    <color name="disableText">@color/gray5</color>
    <color name="goldSubText">@color/gold</color>
    <color name="goldPrimarySatisfied">@color/gold5</color>
    <color name="deliveryNoteText">@color/gray6</color>
    <color name="ricePoolText">@color/mr</color>

    <color name="alert">@color/mr</color>

    <!-- input text field -->
    <color name="inputLineShadow">@color/black</color>
    <color name="inputLineHighLight">@color/fun_n4</color>
    <color name="inputLineText">@color/gray7</color>

    <!-- focus text field -->
    <color name="textLineShadow">@color/black</color>
    <color name="textLineHighlight">@color/gray6</color>
    <color name="textLineText">@color/gray9</color>

    <!-- text box -->
    <color name="textBoxShadow">@color/black</color>
    <color name="textBoxFill">@color/gray1</color>

    <!-- button -->
    <color name="second_satisfied_button_highlight">@color/gray5</color>
    <color name="second_satisfied_button_shadow">@color/black</color>
    <color name="second_satisfied_button_fill">@color/gray3</color>
    <color name="second_satisfied_button_text">@color/gray9</color>

    <color name="second_unsatisfied_button_highlight">@color/gray5</color>
    <color name="second_unsatisfied_button_shadow">@color/gray3</color>
    <color name="second_unsatisfied_button_fill">@color/gray3</color>
    <color name="second_unsatisfied_button_text">@color/gray5</color>

    <color name="primary_satisfied_button_highlight">@color/gray6</color>
    <color name="primary_satisfied_button_shadow">@color/black</color>
    <color name="primary_unsatisfied_button_highlight">@color/gray2</color>
    <color name="primary_unsatisfied_button_shadow">@color/gray2</color>

    <color name="primary_satisfied_button_fill">@color/gray12</color>
    <color name="primary_satisfied_button_text">@color/gray5</color>


    <color name="background">@color/gray3</color>
    <color name="background_light">@color/gray15</color>

    <color name="bundle_bar_background">@color/gray17</color>
    <color name="bundle_bar_background_selected">@color/gray12</color>
    <color name="bundle_bar_border">@color/gray4</color>
    <color name="bundle_bar_text">@color/gray7</color>
    <color name="bundle_bar_selected_border">@color/gray15</color>
    <color name="bundle_bar_selected_text">@color/gray5</color>


    <!-- the card of background color -->
    <color name="card_background">#ff393939</color>
    <color name="card_top_shadow">@color/gray1</color>
    <color name="card_border">@color/gray4</color>

    <!-- map -->
    <color name="rest_address_shadow">@color/red6</color>
    <color name="rest_address_fill">@color/red4</color>
    <color name="rest_address_text">@color/white</color>
    <color name="home_address_shadow">@color/gray4</color>
    <color name="home_address_fill">@color/gray6</color>
    <color name="home_address_text">@color/white</color>
    <color name="driver_polyline">#ffafafaf</color>
    <color name="driver_polyline_shadow">#40000000</color>
    <color name="driver_polyline_shadow2">#45000000</color>

    <!-- tag -->
    <color name="tag_shadow_dark">@color/gray7</color>

    <!-- restaurant horizontal -->
    <color name="restaurant_see_all_shadow">#bb000000</color>

    <!-- lucky -->
    <color name="luckyMenuShadow">#ff622c27</color>
    <color name="luckyMenuBorder">@color/red_8</color>
    <color name="luckyMenuSelectedBorder">@color/gray4</color>
    <color name="luckyMenuSelected">@color/red4</color>
    <color name="luckyRefreshBackground">@color/gray3</color>
    <color name="luckyRefreshBorder">@color/gray4</color>
    <color name="luckyMenuTextColor">@color/red_10</color>

    <!-- fast delivery -->
    <color name="fast_delivery_progress_color">#ffb15e58</color>
    <color name="fast_delivery_progress_shadow_color">#ff693e3c</color>
    <color name="fast_delivery_background_color">#ff1e1e1e</color>
    <color name="fast_delivery_background_shadow_color">@color/black</color>
    <color name="fast_delivery_label_color">@color/gray7</color>

    <color name="indicator_normal">#ff2b2b2b</color>
    <color name="indicator_normal_shadow">@color/black</color>
    <color name="indicator_selected">@color/gray7</color>

    <color name="pickup_item_bg">@color/gray5</color>
    <color name="pickup_item_shadow">@color/gray1</color>

    <color name="place_order_shadow">#57000000</color>

    <color name="cart_count">@color/gray5</color>
    <color name="cart_name">@color/white</color>
    <color name="tab_item">@color/gray7</color>
    <color name="tab_item_pressed">@color/white</color>

    <color name="pb_menu_recommend_default">@color/gray9</color>
    <color name="pb_menu_recommend">@color/gray6</color>

    <color name="refer_main_text">@color/gray9</color>
    <color name="refer_sub_text">@color/gray7</color>
    <color name="refer_button_text">#ffffffff</color>

    <color name="restaurant_name_shadow">#59000000</color>

    <color name="stroke_text">@color/gray5</color>
    <color name="pickup_restaurant_name">@color/white</color>

    <color name="map_circle_border_shadow">@color/black</color>

    <color name="food_cascade_top_shadow">@color/gray1</color>
    <color name="food_cascade_bottom_shadow">@color/gray4</color>


</resources>