<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- font-weight -->
    <!--    100 - Thin -->
    <!--    200 - Extra Light (Ultra Light) -->
    <!--    300 - Light -->
    <!--    400 - Regular (Normal、Book、Roman) -->
    <!--    500 - Medium -->
    <!--    600 - Semi Bold (Demi Bold) -->
    <!--    700 - Bold -->
    <!--    800 - Extra Bold (Ultra Bold) -->
    <!--    900 - Black (Heavy) -->
    <style name="FontH1">
        <item name="android:fontFamily">@font/font_bold</item>
        <item name="android:textSize">@dimen/font_size_h1</item>
        <item name="android:lineSpacingExtra">1.6dp</item>
    </style>

    <style name="FontH2">
        <item name="android:fontFamily">@font/font_bold</item>
        <item name="android:textSize">@dimen/font_size_h2</item>
        <item name="android:lineSpacingExtra">1.6dp</item>
    </style>

    <style name="FontH3">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h3</item>
        <item name="android:lineSpacingMultiplier">1.0</item>
    </style>

    <style name="FontH4">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h4</item>
        <item name="android:lineSpacingExtra">1.2dp</item>
    </style>

    <style name="FontH5">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h5</item>
        <item name="android:lineSpacingExtra">1.2dp</item>
    </style>

    <style name="FontH6">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h6</item>
        <item name="android:lineSpacingMultiplier">1.1</item>
        <item name="android:letterSpacing">0.05</item>
    </style>

    <style name="FontH6extra">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h6</item>
        <item name="android:lineSpacingExtra">1.2dp</item>
    </style>

    <style name="FontH7">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h7</item>
        <item name="android:lineSpacingExtra">1.2dp</item>
    </style>

    <style name="FontH8">
        <item name="android:fontFamily">@font/font_regular</item>
        <item name="android:textSize">@dimen/font_size_h8</item>
        <item name="android:lineSpacingExtra">1.0dp</item>
    </style>

    <style name="FontH8s">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h8</item>
        <item name="android:lineSpacingExtra">1.0dp</item>
    </style>

    <style name="FontH9">
        <item name="android:fontFamily">@font/font_semibold</item>
        <item name="android:textSize">@dimen/font_size_h9</item>
        <item name="android:lineSpacingExtra">1.2dp</item>
    </style>

    <!-- menu item font for normal label -->
    <style name="RegularItemStyle" parent="ItemStyle">
        <item name="android:fontFamily">@font/font_regular</item>
    </style>

</resources>