<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="EditTextStyle" parent="FontH5">
        <item name="android:shadowColor">@color/gray5</item>
        <item name="android:textColorHighlight">@color/white</item>
        <item name="android:textColor">@color/gray5</item>
        <item name="android:textCursorDrawable">@drawable/text_cursor</item>
        <item name="android:background">@drawable/edittext_bottom_line</item>
    </style>

    <!-- out of editext add text input edittext to set bottom line -->
    <style name="EditTextNoBottomLineStyle">
        <item name="android:shadowColor">@color/gray5</item>
        <item name="android:textColorHighlight">@color/white</item>
        <item name="android:textColor">@color/gray5</item>
        <item name="android:textCursorDrawable">@drawable/text_cursor</item>
        <item name="android:background">@null</item>
    </style>

    <style name="EditTextBorderStyle">
        <item name="android:shadowColor">@color/gray5</item>
        <item name="android:textColorHighlight">@color/white</item>
        <item name="android:textColor">@color/gray5</item>
        <item name="android:textCursorDrawable">@drawable/text_cursor</item>
        <item name="android:textSize">@dimen/font_size_h5</item>
        <item name="android:background">@drawable/edittext_border_line</item>
    </style>


</resources>