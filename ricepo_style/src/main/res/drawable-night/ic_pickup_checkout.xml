<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="26dp"
    android:height="26dp"
    android:viewportWidth="26"
    android:viewportHeight="26">
  <group>
    <clip-path
        android:pathData="M0,0H26V26H0z"/>
    <path
        android:pathData="M8.296,15.589a6.044,6.044 0,0 1,2.2 0.388h5.37a2.078,2.078 0,0 1,1.423 0.647l2.184,-2.368 -0.168,-8.741c0.083,-0.046 -12.02,0.04 -12.128,0v9.773z"
        android:fillColor="@color/gray8"/>
    <path
        android:pathData="M22.336,14.697a1.663,1.663 0,0 0,-2.07 0.18l-0.712,0.841c-0.065,0.129 -1.747,1.812 -1.812,1.876v0.259a1.808,1.808 0,0 1,-1.812 1.812h-3.946a0.453,0.453 0,0 1,0 -0.906h3.947a0.884,0.884 0,0 0,0.906 -0.906,0.884 0.884,0 0,0 -0.906,-0.906h-5.305a1.1,1.1 0,0 1,-0.453 -0.065A5.29,5.29 0,0 0,7.52 16.559a2.3,2.3 0,0 0,-0.647 0.129,5.43 5.43,0 0,0 -3.623,3.3l4.065,4.331 0.94,-0.728a1.772,1.772 0,0 1,1.485 -0.631h5.977a3.9,3.9 0,0 0,2.675 -0.988l4.158,-5.025a1.746,1.746 0,0 0,-0.216 -2.25z"
        android:fillColor="@color/gray8"/>
    <path
        android:strokeWidth="1"
        android:pathData="M10.7588,6.064v-2.491a2.379,2.379 0,0 1,2.29 -2.352,2.469 2.469,0 0,1 2.422,2.352v2.491"
        android:fillColor="#00000000"
        android:strokeColor="@color/gray8"/>
  </group>
  <path
      android:pathData="M17.312,15.88l-0.366,-0.366c-0.314,-0.313 -0.714,-0.5 -1.07,-0.5h-5.466l-0.09,-0.036c-0.107,-0.043 -0.227,-0.063 -0.377,-0.063 -0.2,0 -0.427,0.036 -0.666,0.075 -0.273,0.044 -0.581,0.094 -0.903,0.096l-1.017,0.073 -0.536,0.039L6.821,5.359l0.604,0.225c0.108,0.002 0.397,0.006 1.15,0.006 1.08,0 2.686,-0.006 4.387,-0.013 1.696,-0.006 3.45,-0.012 4.625,-0.012 0.469,0 0.814,0 1.055,0.003 0.13,0 0.23,0.002 0.3,0.004 0.118,0.003 0.478,0.012 0.574,0.38l0.097,0.373 -0.081,0.046 0.014,6.912v0.197l-0.134,0.145 -1.747,1.876 -0.353,0.38z"
      android:strokeLineJoin="round"
      android:fillColor="@color/gray8"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M17.299,15.16l1.747,-1.876 -0.015,-7.205c0.067,-0.038 -7.42,0.01 -10.455,0.01 -0.754,0 -1.234,-0.002 -1.255,-0.01v8.582l1.035,-0.075c0.776,0 1.503,-0.331 2.15,-0.073h5.37c0.517,0 1.035,0.26 1.423,0.647m0,1c-0.265,0 -0.52,-0.105 -0.707,-0.292 -0.218,-0.219 -0.493,-0.355 -0.716,-0.355h-5.37c-0.127,0 -0.253,-0.024 -0.372,-0.071 -0.045,-0.018 -0.108,-0.027 -0.19,-0.027 -0.161,0 -0.368,0.033 -0.587,0.069 -0.287,0.046 -0.61,0.099 -0.963,0.102l-1.001,0.072c-0.277,0.02 -0.55,-0.076 -0.753,-0.265 -0.204,-0.19 -0.319,-0.455 -0.319,-0.732v-8.582c0,-0.328 0.16,-0.634 0.429,-0.821 0.197,-0.138 0.435,-0.198 0.668,-0.175 0.07,0.003 0.314,0.007 1.158,0.007 1.078,0 2.684,-0.006 4.384,-0.013 1.697,-0.006 3.451,-0.012 4.627,-0.012 0.47,0 0.817,0 1.06,0.003 0.132,0 0.236,0.002 0.307,0.004l0.111,0.004c0.554,0.034 0.85,0.425 0.934,0.75 0.025,0.093 0.035,0.188 0.032,0.28l0.015,7.176c0,0.254 -0.095,0.498 -0.268,0.684l-1.747,1.876c-0.185,0.198 -0.443,0.313 -0.714,0.318h-0.018z"
      android:strokeLineJoin="round"
      android:fillColor="@color/gray8"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M7.292,24.707l-0.353,-0.354 -4.012,-4.011 -0.233,-0.233 0.123,-0.306c0.789,-1.973 3.235,-2.565 4.622,-2.743 0.069,-0.008 0.139,-0.013 0.207,-0.013 1.632,0 2.604,2.418 2.841,3.334 0.468,0.129 2.12,0.368 5.927,0.368 0.368,0 1.113,-0.825 1.657,-1.428 0.482,-0.534 0.98,-1.086 1.515,-1.512 0.118,-0.194 0.471,-0.838 0.632,-1.138l-0.094,-0.551 -0.038,-0.22 0.14,-0.176 0.08,-0.101c0.401,-0.51 0.902,-1.145 1.694,-1.145 0.078,0 0.157,0.007 0.237,0.02 0.406,0.065 0.727,0.33 0.88,0.727 0.216,0.562 0.072,1.301 -0.36,1.84l-0.003,0.005 -0.004,0.005 -4.076,4.852 -0.014,0.017 -0.015,0.015c-0.751,0.751 -1.8,1.182 -2.877,1.182L9.751,23.141c-0.576,0 -1.094,0.2 -1.458,0.565l-0.647,0.647 -0.354,0.354z"
      android:strokeLineJoin="round"
      android:fillColor="@color/gray8"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M22,14.978c-0.59,0 -0.988,0.56 -1.383,1.057l0.123,0.718c-0.065,0.13 -0.72,1.336 -0.784,1.401 -1.337,1.035 -2.572,3.095 -3.542,3.095 -4.206,0 -6.37,-0.3 -6.37,-0.56 0,-0.258 -0.988,-3.331 -2.542,-3.133 -1.553,0.199 -3.574,0.815 -4.221,2.432l4.011,4.012 0.647,-0.647c0.453,-0.453 1.1,-0.712 1.812,-0.712h6.017c0.97,0 1.876,-0.388 2.523,-1.035L22.367,16.753c0.518,-0.647 0.469,-1.652 -0.21,-1.762 -0.054,-0.009 -0.106,-0.013 -0.157,-0.013m0,-1c0.104,0 0.21,0.009 0.316,0.026 0.587,0.094 1.049,0.474 1.267,1.042 0.279,0.721 0.104,1.659 -0.435,2.332l-0.015,0.019 -4.076,4.852c-0.019,0.022 -0.038,0.043 -0.059,0.064 -0.844,0.844 -2.021,1.328 -3.23,1.328L9.751,23.641c-0.442,0 -0.835,0.149 -1.105,0.419l-0.647,0.647c-0.39,0.39 -1.023,0.39 -1.414,0l-4.011,-4.012c-0.283,-0.282 -0.37,-0.707 -0.221,-1.078 0.655,-1.638 2.439,-2.722 5.023,-3.053 0.09,-0.011 0.18,-0.017 0.27,-0.017 0.697,0 1.718,0.346 2.627,1.995 0.227,0.412 0.464,0.953 0.614,1.415 0.662,0.118 2.316,0.29 5.489,0.292 0.279,-0.105 0.93,-0.827 1.324,-1.263 0.474,-0.526 0.964,-1.068 1.501,-1.51 0.116,-0.198 0.342,-0.608 0.495,-0.89L19.631,16.203c-0.048,-0.28 0.026,-0.568 0.203,-0.79l0.079,-0.1c0.444,-0.564 1.052,-1.336 2.087,-1.336z"
      android:strokeLineJoin="round"
      android:fillColor="@color/gray8"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
</vector>
